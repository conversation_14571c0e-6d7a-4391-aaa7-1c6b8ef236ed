#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: cache_monitor.py
@time: 2025/7/23 
@Description: 缓存监控和管理工具
"""
import json
import time
from typing import Dict, Any, List
from dataclasses import dataclass, asdict

from common.myLog import logger
from common.cache_manager import global_cache


@dataclass
class CacheMetrics:
    """缓存指标"""
    timestamp: float
    cache_size: int
    max_size: int
    hit_rate: float
    hits: int
    misses: int
    evictions: int
    expirations: int
    memory_usage_mb: float = 0.0


class CacheMonitor:
    """缓存监控器"""
    
    def __init__(self, cache_manager=None):
        self.cache_manager = cache_manager or global_cache
        self.metrics_history: List[CacheMetrics] = []
        self.max_history_size = 1000
        
    def collect_metrics(self) -> CacheMetrics:
        """收集缓存指标"""
        stats = self.cache_manager.get_stats()
        
        metrics = CacheMetrics(
            timestamp=time.time(),
            cache_size=stats["cache_size"],
            max_size=stats["max_size"],
            hit_rate=stats["hit_rate"],
            hits=stats["hits"],
            misses=stats["misses"],
            evictions=stats["evictions"],
            expirations=stats["expirations"]
        )
        
        # 保存历史记录
        self.metrics_history.append(metrics)
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history.pop(0)
        
        return metrics
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        metrics = self.collect_metrics()
        return asdict(metrics)
    
    def get_metrics_summary(self, last_n_minutes: int = 60) -> Dict[str, Any]:
        """获取指标摘要"""
        current_time = time.time()
        cutoff_time = current_time - (last_n_minutes * 60)
        
        recent_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "没有足够的历史数据"}
        
        # 计算平均值
        avg_hit_rate = sum(m.hit_rate for m in recent_metrics) / len(recent_metrics)
        avg_cache_size = sum(m.cache_size for m in recent_metrics) / len(recent_metrics)
        total_evictions = sum(m.evictions for m in recent_metrics)
        total_expirations = sum(m.expirations for m in recent_metrics)
        
        return {
            "time_period_minutes": last_n_minutes,
            "sample_count": len(recent_metrics),
            "average_hit_rate": round(avg_hit_rate, 4),
            "average_cache_size": round(avg_cache_size, 2),
            "total_evictions": total_evictions,
            "total_expirations": total_expirations,
            "current_metrics": asdict(recent_metrics[-1]) if recent_metrics else None
        }
    
    def check_cache_health(self) -> Dict[str, Any]:
        """检查缓存健康状态"""
        metrics = self.collect_metrics()
        health_status = {
            "status": "healthy",
            "warnings": [],
            "recommendations": []
        }
        
        # 检查命中率
        if metrics.hit_rate < 0.5:
            health_status["warnings"].append("缓存命中率较低")
            health_status["recommendations"].append("考虑调整缓存策略或增加缓存大小")
        
        # 检查缓存使用率
        usage_rate = metrics.cache_size / metrics.max_size
        if usage_rate > 0.9:
            health_status["warnings"].append("缓存使用率过高")
            health_status["recommendations"].append("考虑增加缓存大小或减少TTL")
        
        # 检查驱逐率
        if len(self.metrics_history) > 1:
            recent_evictions = metrics.evictions - self.metrics_history[-2].evictions
            if recent_evictions > 10:
                health_status["warnings"].append("缓存驱逐频繁")
                health_status["recommendations"].append("考虑增加缓存大小")
        
        if health_status["warnings"]:
            health_status["status"] = "warning"
        
        return health_status
    
    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标数据"""
        if format_type.lower() == "json":
            return json.dumps([asdict(m) for m in self.metrics_history], indent=2)
        else:
            raise ValueError(f"不支持的格式: {format_type}")


class CacheManager:
    """缓存管理工具"""
    
    def __init__(self):
        self.monitor = CacheMonitor()
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        return {
            "current_metrics": self.monitor.get_current_metrics(),
            "summary_1h": self.monitor.get_metrics_summary(60),
            "summary_24h": self.monitor.get_metrics_summary(1440),
            "health_check": self.monitor.check_cache_health(),
            "cache_info": global_cache.get_cache_info()
        }
    
    def optimize_cache(self) -> Dict[str, Any]:
        """缓存优化建议"""
        health = self.monitor.check_cache_health()
        current_metrics = self.monitor.get_current_metrics()
        
        optimizations = {
            "performed": [],
            "recommendations": health.get("recommendations", [])
        }
        
        # 自动清理过期项
        initial_size = current_metrics["cache_size"]
        global_cache._cleanup_expired()
        final_size = global_cache.get_stats()["cache_size"]
        
        if initial_size > final_size:
            optimizations["performed"].append(
                f"清理了 {initial_size - final_size} 个过期缓存项"
            )
        
        return optimizations
    
    def clear_cache_by_pattern(self, pattern: str) -> int:
        """按模式清理缓存"""
        cache_info = global_cache.get_cache_info()
        cleared_count = 0
        
        keys_to_clear = [
            key for key in cache_info.keys() 
            if pattern in key
        ]
        
        for key in keys_to_clear:
            if global_cache.delete(key):
                cleared_count += 1
        
        logger.info(f"按模式 '{pattern}' 清理了 {cleared_count} 个缓存项")
        return cleared_count
    
    def get_cache_report(self) -> str:
        """生成缓存报告"""
        dashboard_data = self.get_dashboard_data()
        
        report = f"""
=== 缓存系统报告 ===
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

当前状态:
- 缓存大小: {dashboard_data['current_metrics']['cache_size']}/{dashboard_data['current_metrics']['max_size']}
- 命中率: {dashboard_data['current_metrics']['hit_rate']:.2%}
- 总请求数: {dashboard_data['current_metrics']['hits'] + dashboard_data['current_metrics']['misses']}

24小时摘要:
- 平均命中率: {dashboard_data['summary_24h'].get('average_hit_rate', 0):.2%}
- 平均缓存大小: {dashboard_data['summary_24h'].get('average_cache_size', 0):.1f}
- 总驱逐次数: {dashboard_data['summary_24h'].get('total_evictions', 0)}
- 总过期次数: {dashboard_data['summary_24h'].get('total_expirations', 0)}

健康检查:
- 状态: {dashboard_data['health_check']['status']}
- 警告: {', '.join(dashboard_data['health_check']['warnings']) if dashboard_data['health_check']['warnings'] else '无'}
- 建议: {', '.join(dashboard_data['health_check']['recommendations']) if dashboard_data['health_check']['recommendations'] else '无'}
"""
        return report


# 全局缓存管理器实例
cache_manager = CacheManager()
