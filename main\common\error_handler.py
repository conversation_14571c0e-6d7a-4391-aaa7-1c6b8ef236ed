#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: error_handler.py
@time: 2025/4/15 10:00
@Description: 错误处理工具类
"""
import functools
import json
from typing import Callable, Any, Dict, Optional

import streamlit as st

from common.myLog import logger


class ErrorHandler:
    """错误处理工具类"""
    
    @staticmethod
    def handle_json_error(func: Callable) -> Callable:
        """处理JSON解析错误的装饰器
        
        Args:
            func: 被装饰的函数
        
        Returns:
            装饰后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except json.JSONDecodeError as e:
                error_msg = f"JSON解析错误: {str(e)}"
                st.error(error_msg)
                logger.error(error_msg)
                return None
        return wrapper
    
    @staticmethod
    def handle_api_error(func: Callable) -> Callable:
        """处理API调用错误的装饰器
        
        Args:
            func: 被装饰的函数
        
        Returns:
            装饰后的函数
        """
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_msg = f"API调用错误: {str(e)}"
                st.error(error_msg)
                logger.error(error_msg)
                return None
        return wrapper
    
    @staticmethod
    def show_error_message(error: Exception, custom_message: Optional[str] = None) -> None:
        """显示错误消息
        
        Args:
            error: 异常对象
            custom_message: 自定义错误消息前缀
        """
        message = custom_message or "发生错误"
        error_msg = f"{message}: {str(error)}"
        st.error(error_msg)
        logger.error(error_msg)

    @classmethod
    def safe_execute(cls, func: Callable, error_message: str, *args, **kwargs) -> Optional[Any]:
        """安全执行函数
        
        Args:
            func: 要执行的函数
            error_message: 错误消息
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果或None（如果出错）
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            cls.show_error_message(e, error_message)
            return None 