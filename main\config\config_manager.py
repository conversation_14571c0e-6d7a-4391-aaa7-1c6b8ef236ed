#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: config_manager.py
@time: 2025/7/23 
@Description: 高级配置管理器
"""
import os
import json
import yaml
import threading
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from functools import lru_cache
from contextlib import contextmanager

from pydantic import BaseModel, Field, field_validator, ValidationError
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

from common.myLog import logger


@dataclass
class ConfigSource:
    """配置源信息"""
    source_type: str  # env, file, default
    source_path: Optional[str] = None
    priority: int = 0  # 优先级，数字越大优先级越高
    last_modified: Optional[float] = None


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    @staticmethod
    def validate_database_url(url: str) -> bool:
        """验证数据库URL格式"""
        import re
        db_pattern = re.compile(
            r'^(mysql|postgresql|sqlite|redis)(\+\w+)?://'
            r'([^:]+:[^@]+@)?'  # username:password@
            r'([^:/]+)'  # host
            r'(:\d+)?'  # port
            r'(/\w+)?'  # database
            r'(\?\S+)?$'  # query params
        )
        return db_pattern.match(url) is not None
    
    @staticmethod
    def validate_required_fields(config: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """验证必需字段"""
        missing_fields = []
        for field in required_fields:
            if field not in config or not config[field]:
                missing_fields.append(field)
        return missing_fields


class ConfigManager:
    """高级配置管理器"""
    
    def __init__(self, config_dir: str = None):
        self._lock = threading.RLock()
        self._config_dir = Path(config_dir or os.path.join(os.path.dirname(__file__)))
        self._configs: Dict[str, Any] = {}
        self._sources: Dict[str, ConfigSource] = {}
        self._watchers: Dict[str, Any] = {}
        self._validators = ConfigValidator()
        
        # 加载环境变量
        self._load_env_files()
        
    def _load_env_files(self):
        """加载环境变量文件"""
        env_files = [
            self._config_dir / ".env",
            self._config_dir / f".env.{os.getenv('ENV', 'dev')}",
            self._config_dir / ".env.local"
        ]
        
        for env_file in env_files:
            if env_file.exists():
                load_dotenv(env_file, override=True)
                logger.info(f"加载环境变量文件: {env_file}")
    
    def load_config(
        self, 
        config_name: str, 
        config_path: Optional[str] = None,
        config_type: str = "auto"
    ) -> Dict[str, Any]:
        """加载配置文件"""
        with self._lock:
            if config_path is None:
                config_path = self._config_dir / f"{config_name}.yaml"
            else:
                config_path = Path(config_path)
            
            if not config_path.exists():
                logger.warning(f"配置文件不存在: {config_path}")
                return {}
            
            try:
                # 根据文件扩展名确定类型
                if config_type == "auto":
                    config_type = config_path.suffix.lower()[1:]  # 去掉点号
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    if config_type in ['yaml', 'yml']:
                        config_data = yaml.safe_load(f)
                    elif config_type == 'json':
                        config_data = json.load(f)
                    else:
                        raise ValueError(f"不支持的配置文件类型: {config_type}")
                
                # 记录配置源
                self._sources[config_name] = ConfigSource(
                    source_type="file",
                    source_path=str(config_path),
                    priority=1,
                    last_modified=config_path.stat().st_mtime
                )
                
                self._configs[config_name] = config_data
                logger.info(f"成功加载配置: {config_name} from {config_path}")
                return config_data
                
            except Exception as e:
                logger.error(f"加载配置文件失败 {config_path}: {str(e)}")
                raise
    
    def get_config(self, config_name: str, default: Any = None) -> Any:
        """获取配置值"""
        with self._lock:
            return self._configs.get(config_name, default)
    
    def set_config(self, config_name: str, value: Any, source_type: str = "runtime"):
        """设置配置值"""
        with self._lock:
            self._configs[config_name] = value
            self._sources[config_name] = ConfigSource(
                source_type=source_type,
                priority=2 if source_type == "runtime" else 1
            )
    
    def get_env_config(
        self, 
        key: str, 
        default: Any = None, 
        config_type: type = str,
        required: bool = False
    ) -> Any:
        """从环境变量获取配置"""
        value = os.getenv(key, default)
        
        if required and value is None:
            raise ValueError(f"必需的环境变量未设置: {key}")
        
        if value is None:
            return default
        
        # 类型转换
        try:
            if config_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif config_type == int:
                return int(value)
            elif config_type == float:
                return float(value)
            elif config_type == list:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return value
        except (ValueError, TypeError) as e:
            logger.warning(f"环境变量类型转换失败 {key}: {str(e)}")
            return default
    
    def validate_config(self, config_name: str, schema: Dict[str, Any]) -> List[str]:
        """验证配置"""
        config = self.get_config(config_name, {})
        errors = []
        
        # 检查必需字段
        required_fields = schema.get('required', [])
        missing_fields = self._validators.validate_required_fields(config, required_fields)
        if missing_fields:
            errors.extend([f"缺少必需字段: {field}" for field in missing_fields])
        
        # 检查URL格式
        url_fields = schema.get('url_fields', [])
        for field in url_fields:
            if field in config and not self._validators.validate_url(config[field]):
                errors.append(f"无效的URL格式: {field}")
        
        # 检查数据库URL格式
        db_url_fields = schema.get('db_url_fields', [])
        for field in db_url_fields:
            if field in config and not self._validators.validate_database_url(config[field]):
                errors.append(f"无效的数据库URL格式: {field}")
        
        return errors
    
    def reload_config(self, config_name: str) -> bool:
        """重新加载配置"""
        source = self._sources.get(config_name)
        if not source or source.source_type != "file":
            return False
        
        try:
            self.load_config(config_name, source.source_path)
            logger.info(f"配置重新加载成功: {config_name}")
            return True
        except Exception as e:
            logger.error(f"配置重新加载失败 {config_name}: {str(e)}")
            return False
    
    def watch_config(self, config_name: str, callback=None):
        """监控配置文件变化"""
        # 这里可以实现文件监控逻辑
        # 简化版本，实际可以使用watchdog库
        pass
    
    def export_config(self, config_name: str, export_path: str, format_type: str = "yaml"):
        """导出配置"""
        config = self.get_config(config_name)
        if not config:
            raise ValueError(f"配置不存在: {config_name}")
        
        export_path = Path(export_path)
        
        with open(export_path, 'w', encoding='utf-8') as f:
            if format_type.lower() in ['yaml', 'yml']:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            elif format_type.lower() == 'json':
                json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的导出格式: {format_type}")
        
        logger.info(f"配置导出成功: {config_name} -> {export_path}")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        with self._lock:
            return {
                "loaded_configs": list(self._configs.keys()),
                "sources": {
                    name: {
                        "type": source.source_type,
                        "path": source.source_path,
                        "priority": source.priority
                    }
                    for name, source in self._sources.items()
                },
                "config_dir": str(self._config_dir)
            }


# 全局配置管理器实例
config_manager = ConfigManager()
