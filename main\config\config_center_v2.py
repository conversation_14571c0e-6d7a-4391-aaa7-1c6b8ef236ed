#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: config_center_v2.py
@time: 2025/7/23 
@Description: 新版配置中心 - 优化版本
"""
import os
from functools import lru_cache
from typing import Dict, Any, Optional

from config.config_manager import config_manager
from config.models import AppConfig, load_config_from_env
from common.myLog import logger


class ConfigCenter:
    """配置中心 - 统一配置管理"""
    
    def __init__(self):
        self._app_config: Optional[AppConfig] = None
        self._yaml_config: Dict[str, Any] = {}
        self._initialized = False
    
    def initialize(self):
        """初始化配置中心"""
        if self._initialized:
            return
        
        try:
            # 加载YAML配置文件
            self._yaml_config = config_manager.load_config("config")
            
            # 加载环境变量配置
            self._app_config = load_config_from_env()
            
            # 验证配置
            self._validate_config()
            
            self._initialized = True
            logger.info("配置中心初始化成功")
            
        except Exception as e:
            logger.error(f"配置中心初始化失败: {str(e)}")
            raise
    
    def _validate_config(self):
        """验证配置"""
        if not self._app_config:
            raise ValueError("应用配置未加载")
        
        # 验证数据库配置
        db_schema = {
            'required': ['host', 'port', 'username', 'password', 'database'],
            'db_url_fields': ['url']
        }
        
        # 这里可以添加更多验证逻辑
        logger.info("配置验证通过")
    
    @property
    def app(self) -> AppConfig:
        """获取应用配置"""
        if not self._initialized:
            self.initialize()
        return self._app_config
    
    @property
    def yaml_config(self) -> Dict[str, Any]:
        """获取YAML配置"""
        if not self._initialized:
            self.initialize()
        return self._yaml_config
    
    # 数据库配置属性
    @property
    def mysql_url(self) -> str:
        """MySQL连接URL"""
        return self.app.mysql.url
    
    @property
    def redis_url(self) -> str:
        """Redis连接URL"""
        return self.app.redis.url
    
    @property
    def milvus_config(self) -> Dict[str, Any]:
        """Milvus连接配置"""
        return self.app.milvus.connection_params
    
    # LLM配置属性
    @property
    def llm_config(self) -> Dict[str, Any]:
        """LLM配置"""
        return {
            "base_url": self.app.llm.base_url,
            "api_key": self.app.llm.api_key,
            "model": self.app.llm.model,
            "temperature": self.app.llm.temperature,
            "max_tokens": self.app.llm.max_tokens,
            "timeout": self.app.llm.timeout
        }
    
    @property
    def embedding_config(self) -> Dict[str, Any]:
        """嵌入模型配置"""
        return {
            "model_name": self.app.embedding.model_name,
            "model_path": self.app.embedding.model_path,
            "api_base": self.app.embedding.api_base,
            "api_key": self.app.embedding.api_key,
            "batch_size": self.app.embedding.batch_size,
            "max_length": self.app.embedding.max_length
        }
    
    # 缓存配置
    @property
    def cache_config(self) -> Dict[str, Any]:
        """缓存配置"""
        cache_yaml = self.yaml_config.get("cache", {})
        return {
            "default_ttl": cache_yaml.get("default_ttl", 3600),
            "max_size": cache_yaml.get("max_size", 1000),
            "cleanup_interval": cache_yaml.get("cleanup_interval", 300),
            "ttl_settings": cache_yaml.get("ttl_settings", {})
        }
    
    # 业务配置
    @property
    def business_config(self) -> Dict[str, Any]:
        """业务配置"""
        return self.yaml_config.get("business", {})
    
    @property
    def routing_rules(self) -> Dict[str, Any]:
        """路由规则"""
        return self.business_config.get("routing_rules", {})
    
    @property
    def default_questions(self) -> list:
        """默认问题"""
        return self.business_config.get("default_questions", [])
    
    # 文件处理配置
    @property
    def file_config(self) -> Dict[str, Any]:
        """文件处理配置"""
        return self.yaml_config.get("file_processing", {})
    
    @property
    def max_file_size(self) -> int:
        """最大文件大小"""
        return self.file_config.get("max_file_size", *********)
    
    @property
    def allowed_extensions(self) -> list:
        """允许的文件扩展名"""
        return self.file_config.get("allowed_extensions", ["txt", "pdf", "docx"])
    
    # 检索配置
    @property
    def retrieval_config(self) -> Dict[str, Any]:
        """检索配置"""
        return self.yaml_config.get("retrieval", {})
    
    @property
    def retrieval_strategies(self) -> list:
        """检索策略"""
        return self.retrieval_config.get("strategies", ["Base", "Rerank"])
    
    @property
    def default_retrieval_strategy(self) -> str:
        """默认检索策略"""
        return self.retrieval_config.get("default_strategy", "Rerank")
    
    # 第三方服务配置
    @property
    def dingtalk_config(self) -> Dict[str, Any]:
        """钉钉配置"""
        external = self.yaml_config.get("external_services", {})
        return external.get("dingtalk", {})
    
    @property
    def search_config(self) -> Dict[str, Any]:
        """搜索服务配置"""
        external = self.yaml_config.get("external_services", {})
        return external.get("search", {})
    
    @property
    def langfuse_config(self) -> Dict[str, Any]:
        """Langfuse配置"""
        external = self.yaml_config.get("external_services", {})
        return external.get("langfuse", {})
    
    # 路径配置
    @property
    def paths(self) -> Dict[str, str]:
        """路径配置"""
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return {
            "base_dir": base_dir,
            "logs_dir": os.path.join(base_dir, "logs"),
            "docs_dir": os.path.join(base_dir, "documents"),
            "models_dir": os.path.join(base_dir, "models"),
            "cache_dir": os.path.join(base_dir, "cache"),
            "config_dir": os.path.join(base_dir, "config")
        }
    
    # 环境相关方法
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.app.env == "dev"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.app.env == "prod"
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.app.env == "test"
    
    # 配置管理方法
    def reload_config(self):
        """重新加载配置"""
        self._initialized = False
        self.initialize()
        logger.info("配置已重新加载")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "environment": self.app.env,
            "debug": self.app.debug,
            "version": self.app.version,
            "database": {
                "mysql_host": self.app.mysql.host,
                "redis_host": self.app.redis.host,
                "milvus_host": self.app.milvus.host
            },
            "ai": {
                "llm_model": self.app.llm.model,
                "embedding_model": self.app.embedding.model_name
            },
            "cache": {
                "default_ttl": self.cache_config["default_ttl"],
                "max_size": self.cache_config["max_size"]
            }
        }
    
    def validate_environment(self) -> Dict[str, Any]:
        """验证环境配置"""
        issues = []
        warnings = []
        
        # 检查必需的环境变量
        required_env_vars = [
            "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DATABASE",
            "LLM_API_KEY", "LLM_MODEL", "SECRET_KEY"
        ]
        
        for var in required_env_vars:
            if not os.getenv(var):
                issues.append(f"缺少必需的环境变量: {var}")
        
        # 检查配置合理性
        if self.app.llm.temperature > 1.0:
            warnings.append("LLM温度参数较高，可能影响输出稳定性")
        
        if self.cache_config["default_ttl"] < 300:
            warnings.append("缓存TTL较短，可能影响性能")
        
        return {
            "status": "healthy" if not issues else "error",
            "issues": issues,
            "warnings": warnings
        }


# 全局配置中心实例
@lru_cache(maxsize=1)
def get_config_center() -> ConfigCenter:
    """获取配置中心单例"""
    return ConfigCenter()


# 向后兼容的配置对象
settings = get_config_center()
