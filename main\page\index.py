#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: index.py
@time: 2024/10/14 16:24
@Description:
"""

from typing import NoReturn
from http import HTTPStatus

import streamlit as st
from streamlit.runtime.scriptrunner import StopException

from config.config_center import settings
from const.style import index_css
from const.custom_exception import ConfigError
from common.utils import Year
from common.myLog import logger

logger.name = __name__

class IndexPage:
    """首页渲染类"""
    
    def __init__(self) -> None:
        """初始化首页组件"""
        self.css = index_css
        self.year = Year

    @staticmethod
    def _render_header() -> None:
        """渲染页面头部"""
        try:
            st.markdown("""
                <div class="index-container">
                    <div class="index-container-1">
                        <h1>{}</h1>
                    </div>
                </div>
            """.format(settings.ui.rag_header),
            unsafe_allow_html=True)
        except Exception as e:
            logger.error(f"Failed to render header: {str(e)}")
            raise StopException(f"页面渲染失败: {HTTPStatus.INTERNAL_SERVER_ERROR}")

    def _render_footer(self) -> None:
        """渲染页面底部"""
        try:
            st.markdown("""
                <div class="ym-container">
                    <span>© {} <a href="{}" style="{}">约苗</a></span>
                </div>
            """.format(self.year, settings.business.URL, settings.ui.ym_link_style),
            unsafe_allow_html=True)
        except Exception as e:
            logger.error(f"Failed to render footer: {str(e)}")
            raise StopException(f"页面渲染失败: {HTTPStatus.INTERNAL_SERVER_ERROR}")

    async def render(self) -> NoReturn:
        """渲染完整页面
        
        异步渲染页面的所有组件，包括样式、头部和底部
        
        Raises:
            StopException: 当页面渲染失败时抛出
        """
        try:
            st.markdown(self.css, unsafe_allow_html=True)
            self._render_header()
            self._render_footer()
        except ConfigError as e:
            logger.error(f"Configuration error: {str(e)}")
            raise StopException("配置错误，请检查设置")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise StopException("页面渲染发生未知错误")

async def init_index() -> NoReturn:
    """页面初始化入口函数"""
    page = IndexPage()
    await page.render()
