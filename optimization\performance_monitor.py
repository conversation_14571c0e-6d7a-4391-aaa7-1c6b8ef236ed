#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: performance_monitor.py
@time: 2025/7/24
@Description: 性能监控工具
"""
import time
import psutil
import threading
from functools import wraps
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque

import streamlit as st
from common.myLog import logger


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    timestamp: float
    unit: str = ""
    category: str = "general"


@dataclass
class FunctionProfile:
    """函数性能分析"""
    name: str
    call_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    recent_calls: deque = field(default_factory=lambda: deque(maxlen=100))
    
    def add_call(self, execution_time: float):
        """添加函数调用记录"""
        self.call_count += 1
        self.total_time += execution_time
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
        self.avg_time = self.total_time / self.call_count
        self.recent_calls.append({
            'time': execution_time,
            'timestamp': time.time()
        })


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.function_profiles: Dict[str, FunctionProfile] = {}
        self.system_metrics: deque = deque(maxlen=1000)
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
    def start_monitoring(self, interval: float = 5.0):
        """开始系统监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_system,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"性能监控已启动，监控间隔: {interval}s")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("性能监控已停止")
    
    def _monitor_system(self, interval: float):
        """系统监控循环"""
        while self.monitoring_active:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                
                # 内存使用情况
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                memory_used_mb = memory.used / 1024 / 1024
                
                # 磁盘使用情况
                disk = psutil.disk_usage('/')
                disk_percent = disk.percent
                
                # 网络IO
                net_io = psutil.net_io_counters()
                
                timestamp = time.time()
                
                with self._lock:
                    self.system_metrics.append({
                        'timestamp': timestamp,
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory_percent,
                        'memory_used_mb': memory_used_mb,
                        'disk_percent': disk_percent,
                        'network_bytes_sent': net_io.bytes_sent,
                        'network_bytes_recv': net_io.bytes_recv
                    })
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"系统监控错误: {str(e)}")
                time.sleep(interval)
    
    def add_metric(self, name: str, value: float, unit: str = "", category: str = "general"):
        """添加性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            unit=unit,
            category=category
        )
        
        with self._lock:
            self.metrics.append(metric)
            
            # 保持最近1000条记录
            if len(self.metrics) > 1000:
                self.metrics = self.metrics[-1000:]
    
    def profile_function(self, func_name: str, execution_time: float):
        """记录函数性能"""
        with self._lock:
            if func_name not in self.function_profiles:
                self.function_profiles[func_name] = FunctionProfile(name=func_name)
            
            self.function_profiles[func_name].add_call(execution_time)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        if not self.system_metrics:
            return {}
        
        with self._lock:
            latest = self.system_metrics[-1]
            
            # 计算平均值
            recent_metrics = list(self.system_metrics)[-10:]  # 最近10次
            avg_cpu = sum(m['cpu_percent'] for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m['memory_percent'] for m in recent_metrics) / len(recent_metrics)
            
            return {
                'current': latest,
                'averages': {
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory
                },
                'total_samples': len(self.system_metrics)
            }
    
    def get_function_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取函数性能统计"""
        with self._lock:
            stats = {}
            for name, profile in self.function_profiles.items():
                stats[name] = {
                    'call_count': profile.call_count,
                    'total_time': profile.total_time,
                    'avg_time': profile.avg_time,
                    'min_time': profile.min_time if profile.min_time != float('inf') else 0,
                    'max_time': profile.max_time,
                    'recent_avg': self._calculate_recent_avg(profile)
                }
            return stats
    
    def _calculate_recent_avg(self, profile: FunctionProfile) -> float:
        """计算最近调用的平均时间"""
        if not profile.recent_calls:
            return 0.0
        
        recent_times = [call['time'] for call in profile.recent_calls]
        return sum(recent_times) / len(recent_times)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        return {
            'system_stats': self.get_system_stats(),
            'function_stats': self.get_function_stats(),
            'metrics_count': len(self.metrics),
            'monitoring_active': self.monitoring_active,
            'top_slow_functions': self._get_top_slow_functions(),
            'performance_summary': self._get_performance_summary()
        }
    
    def _get_top_slow_functions(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最慢的函数"""
        with self._lock:
            sorted_functions = sorted(
                self.function_profiles.values(),
                key=lambda x: x.avg_time,
                reverse=True
            )
            
            return [
                {
                    'name': func.name,
                    'avg_time': func.avg_time,
                    'call_count': func.call_count,
                    'total_time': func.total_time
                }
                for func in sorted_functions[:limit]
            ]
    
    def _get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        system_stats = self.get_system_stats()
        function_stats = self.get_function_stats()
        
        total_function_time = sum(
            stats['total_time'] for stats in function_stats.values()
        )
        
        total_function_calls = sum(
            stats['call_count'] for stats in function_stats.values()
        )
        
        return {
            'total_function_time': total_function_time,
            'total_function_calls': total_function_calls,
            'avg_function_time': total_function_time / total_function_calls if total_function_calls > 0 else 0,
            'current_cpu': system_stats.get('current', {}).get('cpu_percent', 0),
            'current_memory': system_stats.get('current', {}).get('memory_percent', 0)
        }


# 全局性能监控器
performance_monitor = PerformanceMonitor()


def profile(func_name: Optional[str] = None):
    """性能分析装饰器"""
    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                performance_monitor.profile_function(name, execution_time)
                
                # 记录慢函数
                if execution_time > 1.0:
                    logger.warning(f"慢函数检测: {name} 耗时 {execution_time:.2f}s")
        
        return wrapper
    return decorator


def monitor_startup_performance():
    """监控启动性能"""
    startup_start = time.time()
    
    def log_startup_complete():
        startup_time = time.time() - startup_start
        performance_monitor.add_metric(
            "startup_time", 
            startup_time, 
            "seconds", 
            "startup"
        )
        logger.info(f"应用启动完成，总耗时: {startup_time:.2f}s")
    
    # 注册启动完成回调
    st.session_state.startup_complete_callback = log_startup_complete
    
    # 开始系统监控
    performance_monitor.start_monitoring()


def display_performance_dashboard():
    """显示性能仪表板"""
    if st.sidebar.button("🔍 性能监控"):
        st.subheader("性能监控仪表板")
        
        # 获取性能报告
        report = performance_monitor.get_performance_report()
        
        # 系统状态
        col1, col2, col3 = st.columns(3)
        
        system_stats = report.get('system_stats', {})
        current = system_stats.get('current', {})
        
        with col1:
            st.metric(
                "CPU使用率", 
                f"{current.get('cpu_percent', 0):.1f}%"
            )
        
        with col2:
            st.metric(
                "内存使用率", 
                f"{current.get('memory_percent', 0):.1f}%"
            )
        
        with col3:
            st.metric(
                "内存使用量", 
                f"{current.get('memory_used_mb', 0):.0f}MB"
            )
        
        # 函数性能统计
        st.subheader("函数性能统计")
        function_stats = report.get('function_stats', {})
        
        if function_stats:
            import pandas as pd
            
            df_data = []
            for name, stats in function_stats.items():
                df_data.append({
                    '函数名': name,
                    '调用次数': stats['call_count'],
                    '平均耗时(s)': f"{stats['avg_time']:.3f}",
                    '总耗时(s)': f"{stats['total_time']:.3f}",
                    '最大耗时(s)': f"{stats['max_time']:.3f}"
                })
            
            df = pd.DataFrame(df_data)
            st.dataframe(df, use_container_width=True)
        
        # 最慢函数
        st.subheader("最慢函数 TOP 5")
        top_slow = report.get('top_slow_functions', [])
        
        for i, func in enumerate(top_slow, 1):
            st.write(f"{i}. **{func['name']}** - 平均耗时: {func['avg_time']:.3f}s")


# 自动启动性能监控
if 'performance_monitoring_started' not in st.session_state:
    monitor_startup_performance()
    st.session_state.performance_monitoring_started = True
