#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: constants.py
@time: 2024/11/05 13:44
@Description:
"""
from typing import Any
from pydantic import BaseModel
from collections import OrderedDict

class UpdateUser(BaseModel):
    dt_userid: str = None
    is_active: bool = None
    is_admin: bool = None
    is_super: bool = None
    roles_id: int = None


class UpdateRole(BaseModel):
    id: int = None
    role_name: str  = None
    auth_id: str = None
    remark: str = None


class UpdateConfig(BaseModel):
    id: int = None
    config_value: str  = None
    remark: str = None


DATA_CODING: dict[str, Any] = {
    'mysql_engine': 'InnoDB',
    'mysql_auto_increment': '1',
    'mysql_charset': 'utf8mb4',
    'mysql_collate': 'utf8mb4_unicode_ci',
}

USER_TABLE_COLUMNS: dict[str, Any] = {
    "id": "ID",
    "dt_userid": "用户ID",
    "dt_name": "用户名称",
    "dt_title": "职称",
    "dt_mobile": "手机号",
    "dt_unionid": None,
    "roles_id": "",
    "is_active": "激活状态",
    "is_super": "超级管理员",
    "is_admin": "管理员",
    "create_time": "创建时间",
    "modify_time": "更新时间",
}


ROLE_TABLE_COLUMNS: dict[str, Any] = {
    "id": "ID",
    "role_name": "权限名称",
    "auth_id": "权限ID",
    "remark": "备注",
    "yn": None,
    "create_time": "创建时间",
    "modify_time": "更新时间",
}


AUTH_TABLE_COLUMNS: dict[str, Any] = {
    "id": "ID",
    "auth_name": "权限名称",
    "remark": "备注",
    "yn": None,
    "create_time": "创建时间",
    "modify_time": "更新时间",
}

CONFIG_TABLE_COLUMNS: dict[str, Any] = {
    "id": "ID",
    "config_name": "配置名称",
    "config_value": "配置内容",
    "remark": "备注",
    "create_name": "创建人",
    "yn": None,
    "create_time": "创建时间",
    "modify_time": "更新时间",
}

KNOWLEDGE_TABLE_COLUMNS: dict[str, Any] = {
    "id": "ID",
    "document_name": "文件名称",
    "section": "所属部门",
    "group": "所属组",
    "relevance_ai": "关联AI",
    "create_name": "上传人",
    "yn": None,
    "create_time": "上传时间",
    "modify_time": "更新时间",
}


Q_MAP = OrderedDict([
    ('TQ', '技术问答'),
    ('PQ', '产品问答'),
    ('CSQ', '业务问答'),
    ('AQ', '制度问答'),
    ('TSL', '通识类问答'),
])
