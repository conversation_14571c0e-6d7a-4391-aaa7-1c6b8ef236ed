#!/usr/bin/env python
# -*- coding: utf-8 -*-

import streamlit as st
from main.agent.CaseAgent import app, TestCase, RequirementAnalysis
import json

st.set_page_config(page_title="测试用例生成器", page_icon="📋", layout="wide")

st.title("📋 测试用例生成器 - 人机协同模式")
st.markdown("""
这个工具使用LangGraph实现人机协同的测试用例自动生成。流程包括：
1. 需求分析
2. 人工审核需求分析结果
3. 测试用例生成
4. 质量验证
""")

# 初始化会话状态
if "thread_id" not in st.session_state:
    st.session_state.thread_id = "test_case_session_1"
    
if "current_state" not in st.session_state:
    st.session_state.current_state = None
    
if "human_review_required" not in st.session_state:
    st.session_state.human_review_required = False
    
if "generated_test_cases" not in st.session_state:
    st.session_state.generated_test_cases = []
    
if "requirements_analysis" not in st.session_state:
    st.session_state.requirements_analysis = None

# 侧边栏配置
with st.sidebar:
    st.header("⚙️ 配置")
    thread_id = st.text_input("会话ID", value=st.session_state.thread_id)
    st.session_state.thread_id = thread_id
    
    st.markdown("---")
    st.header("📂 输入方式")
    input_method = st.radio("选择输入方式", ["文件路径", "直接输入"])
    
    if input_method == "文件路径":
        file_path = st.text_input("文件路径", placeholder="请输入文件路径")
    else:
        requirements_text = st.text_area("需求内容", height=200, placeholder="在此输入需求内容...")
    
    st.markdown("---")
    st.button("🔄 重置会话", on_click=lambda: st.session_state.clear())

# 主界面
col1, col2 = st.columns(2)

with col1:
    st.header("📥 输入")
    if st.button("🚀 开始生成测试用例", type="primary"):
        # 准备输入内容
        if input_method == "文件路径":
            if not file_path:
                st.error("请输入文件路径")
                st.stop()
            input_content = file_path
        else:
            if not requirements_text:
                st.error("请输入需求内容")
                st.stop()
            input_content = requirements_text
            
        # 配置
        config = {"configurable": {"thread_id": st.session_state.thread_id}}
        
        # 执行工作流
        try:
            with st.spinner("正在处理中..."):
                result = app.invoke({
                    "input_content": input_content,
                    "need_human_review": False
                }, config=config)
                
                st.session_state.current_state = result
                
                if result.get("error_info"):
                    st.error(f"执行失败: {result['error_info']}")
                else:
                    st.success("测试用例生成完成！")
                    if "parsed_requirements" in result:
                        st.session_state.requirements_analysis = result["parsed_requirements"]
                    if "test_cases" in result:
                        st.session_state.generated_test_cases = result["test_cases"]
                        
        except Exception as e:
            st.error(f"执行异常: {str(e)}")

with col2:
    st.header("📊 输出")
    
    # 显示需求分析结果
    if st.session_state.requirements_analysis:
        st.subheader("🔍 需求分析结果")
        st.json(st.session_state.requirements_analysis)
        
        # 人工审核需求分析
        with st.expander("📝 人工审核需求分析", expanded=True):
            feedback = st.text_area("请输入您的反馈意见:", 
                                  placeholder="如有需要修改的地方，请在此说明...",
                                  key="req_feedback")
            if st.button("✅ 确认需求分析"):
                st.success("需求分析已确认")
                st.session_state.human_review_required = False
    
    # 显示测试用例
    if st.session_state.generated_test_cases:
        st.subheader("📋 生成的测试用例")
        for i, case in enumerate(st.session_state.generated_test_cases):
            try:
                validated_case = TestCase(**case)
                with st.expander(f"_TestCase {validated_case.case_id}: {validated_case.description}_"):
                    st.markdown(f"**用例编号:** {validated_case.case_id}")
                    st.markdown(f"**用例描述:** {validated_case.description}")
                    st.markdown(f"**测试类型:** {validated_case.test_type}")
                    st.markdown(f"**优先级:** {validated_case.priority}")
                    st.markdown("**前置条件:**")
                    for precondition in validated_case.preconditions:
                        st.markdown(f"- {precondition}")
                    st.markdown("**操作步骤:**")
                    for j, step in enumerate(validated_case.steps, 1):
                        st.markdown(f"{j}. {step}")
                    st.markdown(f"**预期结果:** {validated_case.expected_result}")
            except Exception as e:
                st.error(f"测试用例格式错误: {str(e)}")
                st.json(case)
        
        # 人工审核测试用例
        with st.expander("📝 人工审核测试用例"):
            test_feedback = st.text_area("请输入对测试用例的反馈意见:", 
                                       placeholder="如有需要修改的地方，请在此说明...",
                                       key="test_feedback")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ 确认测试用例"):
                    st.success("测试用例已确认")
            with col2:
                if st.button("🔁 重新生成"):
                    st.info("重新生成测试用例功能将在后续版本中实现")
        
        # 导出功能
        st.subheader("💾 导出")
        if st.button("📤 导出为JSON"):
            st.download_button(
                label="下载测试用例JSON文件",
                data=json.dumps(st.session_state.generated_test_cases, ensure_ascii=False, indent=2),
                file_name="test_cases.json",
                mime="application/json"
            )