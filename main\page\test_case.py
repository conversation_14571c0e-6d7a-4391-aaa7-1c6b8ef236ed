#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: test_case.py
@time: 2025/3/25 15:26
@Description: 测试用例生成页面
"""
from typing import List, Dict, Any
from pydantic import BaseModel, Field

import streamlit as st

from common.utils import get_system_avatar, ExtractTestCase, remove_empty_lines
from common.markdown_analysis import MarkdownAnalysis
from common.base_ui import BaseUI
from common.base_generator import BaseGenerator
from common.error_handler import <PERSON>rrorHandler
from config.config_center import settings
from common.myLog import logger
from core.prompt import prompt_manager

logger.name = __name__

class TestCase(BaseModel):
    """测试用例数据模型"""
    case_id: str = Field(..., alias="用例编号", min_length=5, description="LOGIN-AUTH-001格式的用例标识")
    case_title: str = Field(..., alias="用例标题", min_length=10)
    model: str = Field(..., alias="模块", min_length=5)
    priority: str = Field("中", alias="优先级", pattern="P0|P1|P2|P3")
    preconditions: List[str] = Field(default_factory=list, alias="前置条件")
    case_datas: List[str] = Field(default_factory=list, alias="测试数据")
    steps: List[str] = Field(..., alias="操作步骤")
    expected_result: str = Field(..., alias="预期结果", min_length=5)

    class Config:
        populate_by_name = True

class TestCaseGenerator(BaseGenerator):
    """测试用例生成器"""
    
    def __init__(self):
        super().__init__(
            prompt_name="testcase_prompt",
        )

class TestCaseUI(BaseUI):
    """测试用例生成页面UI组件"""
    
    def __init__(self):
        super().__init__(
            session_key="case_messages",
            welcome_message="您好！我是 :red[测试用例]智能助手，您只需提供需求文档，我就能快速为您生成精准且全面的测试用例啦。"
        )
        self.generator = TestCaseGenerator()

    async def render_header(self):
        """渲染页面头部"""
        await super().render_header("用例")

    async def process_test_case(self, prompt: str):
        """处理测试用例生成"""
        with st.chat_message("assistant", avatar=get_system_avatar()):
            with st.spinner("正在全力以赴为您生成精准的测试用例，请耐心等待片刻..."):
                await self._generate_and_display_cases(prompt)

    @ErrorHandler.handle_api_error
    async def _generate_and_display_cases(self, prompt: str):
        """生成并显示测试用例"""
        question = {"question": prompt}
        
        with st.expander("用例分析 - 点击展开查看测试用例的详细分析内容", expanded=False, icon="🧪"):
            response_container = st.empty()
            full_response = ""

            async for response in self.generator.generate_content(
                    question,
                    st.session_state.userInfo["dt_userid"]
            ):
                if response:
                    full_response += response
                    with response_container:
                        st.markdown(full_response)

        json_data = ExtractTestCase(full_response)
        self._display_test_cases(json_data)
        self.add_assistant_message(full_response)

    @staticmethod
    def _display_test_cases(json_data: List[Dict[str, Any]]):
        """显示测试用例"""
        if not json_data:
            st.warning("未能成功提取测试用例数据，请检查输入或重试")
            return
            
        st.dataframe(json_data, use_container_width=True)
        
        markdown_data = ErrorHandler.safe_execute(
            MarkdownAnalysis(json_data).create_markdown,
            "生成Markdown文档失败"
        )
        
        if markdown_data:
            st.download_button(
                label="下载Markdown用例",
                data=markdown_data,
                file_name='testcase.md',
                mime='text/markdown'
            )

async def main():
    """主函数"""
    ui = TestCaseUI()
    await ui.render_header()
    await ui.render_chat_history()

    if prompt := st.chat_input("请输入你的问题，按Enter键发送。"):
        await ui.handle_user_input(prompt)
        await ui.process_test_case(prompt)
