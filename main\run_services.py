#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: run_services.py
@time: 2025/1/22 10:10
@Description: 同时启动FastAPI和Streamlit服务
"""
import os
import multiprocessing
import subprocess

os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-88d06c76-6637-40ba-873e-29e7f5640a96"
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_HOST"] = "http://**************:3000"

def run_fastapi():
    """运行FastAPI服务"""
    subprocess.run(["uvicorn", "ym_api.generate:app", "--host", "0.0.0.0", "--port", "8787"])

def run_streamlit():
    """运行Streamlit服务"""
    subprocess.run(["streamlit", "run", "app.py"])

if __name__ == "__main__":
    # 创建进程
    fastapi_process = multiprocessing.Process(target=run_fastapi)
    streamlit_process = multiprocessing.Process(target=run_streamlit)

    try:
        # 启动进程
        fastapi_process.start()
        streamlit_process.start()

        print("FastAPI 服务运行在: http://localhost:8787")
        print("Streamlit 服务运行在: http://localhost:8899")

        # 等待进程结束
        fastapi_process.join()
        streamlit_process.join()

    except KeyboardInterrupt:
        print("\n正在关闭服务...")
        # 终止进程
        fastapi_process.terminate()
        streamlit_process.terminate()
        # 等待进程完全结束
        fastapi_process.join()
        streamlit_process.join()
        print("服务已关闭") 