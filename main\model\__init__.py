#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: __init__.py.py 
@time: 2024/11/5 9:12
@Description: 
"""
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool

from config.config_center import settings

Base = declarative_base()

engine = create_engine(
    settings.database.SQLALCHEMY_DATABASE_URI,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=0,
    pool_timeout=30,
    pool_recycle=-1,
    pool_pre_ping=True,
    echo=False
)

Session = scoped_session(sessionmaker(bind=engine))