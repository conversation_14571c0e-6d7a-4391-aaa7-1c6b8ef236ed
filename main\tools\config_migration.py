#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: config_migration.py
@time: 2025/7/23 
@Description: 配置迁移工具
"""
import os
import json
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any, List

from common.myLog import logger


class ConfigMigrator:
    """配置迁移工具"""
    
    def __init__(self, old_config_path: str, new_config_dir: str):
        self.old_config_path = Path(old_config_path)
        self.new_config_dir = Path(new_config_dir)
        self.migration_log = []
    
    def migrate_from_old_config(self) -> Dict[str, Any]:
        """从旧配置迁移到新配置"""
        try:
            # 导入旧配置
            import sys
            sys.path.append(str(self.old_config_path.parent))
            
            from config_center import settings as old_settings
            
            # 创建新的配置结构
            new_config = self._create_new_config_structure(old_settings)
            
            # 生成环境变量文件
            env_vars = self._extract_env_variables(old_settings)
            
            # 保存新配置
            self._save_new_config(new_config)
            self._save_env_file(env_vars)
            
            # 备份旧配置
            self._backup_old_config()
            
            logger.info("配置迁移完成")
            return {
                "status": "success",
                "new_config": new_config,
                "env_vars": list(env_vars.keys()),
                "migration_log": self.migration_log
            }
            
        except Exception as e:
            logger.error(f"配置迁移失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "migration_log": self.migration_log
            }
    
    def _create_new_config_structure(self, old_settings) -> Dict[str, Any]:
        """创建新的配置结构"""
        self.migration_log.append("开始创建新配置结构")
        
        new_config = {
            "app": {
                "name": "约苗智能问答助手",
                "version": getattr(old_settings, 'version', '2.0'),
                "debug": False,
                "environment": "${ENV:-dev}"
            },
            "database": {
                "mysql": {
                    "host": "${MYSQL_HOST:-localhost}",
                    "port": "${MYSQL_PORT:-3306}",
                    "username": "${MYSQL_USER}",
                    "password": "${MYSQL_PASSWORD}",
                    "database": "${MYSQL_DATABASE}",
                    "pool_size": 10,
                    "max_overflow": 20
                },
                "redis": {
                    "host": "${REDIS_HOST:-localhost}",
                    "port": "${REDIS_PORT:-6379}",
                    "password": "${REDIS_PASSWORD}",
                    "database": "${REDIS_DB:-0}",
                    "max_connections": 50
                },
                "milvus": {
                    "host": "${MILVUS_HOST}",
                    "port": "${MILVUS_PORT:-19530}",
                    "username": "${MILVUS_USER}",
                    "password": "${MILVUS_PASSWORD}",
                    "database": "${MILVUS_DATABASE:-default}"
                }
            },
            "ai": {
                "llm": {
                    "base_url": "${LLM_BASE_URL}",
                    "api_key": "${LLM_API_KEY}",
                    "model": "${LLM_MODEL}",
                    "temperature": getattr(old_settings.llm, 'TEMPERATURE', 0.1),
                    "max_tokens": getattr(old_settings.llm, 'MAX_TOKEN', 4096),
                    "timeout": 60
                },
                "embedding": {
                    "model_name": "${EMBEDDING_MODEL}",
                    "model_path": "${EMBEDDING_MODEL_PATH}",
                    "batch_size": 32,
                    "max_length": 512
                }
            },
            "cache": {
                "default_ttl": 3600,
                "max_size": 1000,
                "cleanup_interval": 300,
                "ttl_settings": {
                    "router_chain": 1800,
                    "rag_chain": 3600,
                    "common_chain": 1800,
                    "retriever": 7200
                }
            },
            "retrieval": {
                "strategies": getattr(old_settings.llm, 'RETRIEVAL_LIST', ["Base", "Rerank"]),
                "default_strategy": "Rerank",
                "top_k": 5,
                "similarity_threshold": 0.7
            },
            "business": {
                "memory_key": getattr(old_settings.business, 'MEMORY_KEY', 'message_store'),
                "session_timeout": 7200,
                "routing_rules": {
                    "technical": ["API", "SDK", "Docker", "数据库", "部署", "日志"],
                    "business": ["预约", "疫苗", "小程序", "公众号", "订单", "支付"],
                    "admin": ["请假", "报销", "离职", "绩效", "合同", "采购"]
                },
                "default_questions": [
                    "接种卡相关问题有哪些？",
                    "公众号无法访问如何处理？",
                    "关于医生积分发放问题解答",
                    "约苗管家及门诊相关问题？"
                ]
            },
            "external_services": {
                "dingtalk": {
                    "client_id": "${DINGTALK_CLIENT_ID}",
                    "client_secret": "${DINGTALK_CLIENT_SECRET}",
                    "api_url": getattr(old_settings.dingtalk, 'api_url', 'https://api.dingtalk.com'),
                    "oapi_url": getattr(old_settings.dingtalk, 'oapi_url', 'https://oapi.dingtalk.com/')
                },
                "search": {
                    "google": {
                        "api_key": "${GOOGLE_API_KEY}",
                        "search_engine_id": "${GOOGLE_SEARCH_ENGINE_ID}"
                    },
                    "tavily": {
                        "api_key": "${TAVILY_API_KEY}",
                        "max_results": 5
                    }
                },
                "langfuse": {
                    "public_key": "${LANGFUSE_PUBLIC_KEY}",
                    "secret_key": "${LANGFUSE_SECRET_KEY}",
                    "host": "${LANGFUSE_HOST}"
                }
            },
            "security": {
                "secret_key": "${SECRET_KEY}",
                "jwt_algorithm": "HS256",
                "jwt_expire_hours": 24,
                "allowed_hosts": ["localhost", "127.0.0.1"],
                "cors_origins": ["http://localhost:3000", "http://localhost:8501"]
            },
            "logging": {
                "level": "${LOG_LEVEL:-INFO}",
                "format": "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
                "handlers": {
                    "console": {"enabled": True, "level": "INFO"},
                    "file": {"enabled": True, "level": "DEBUG", "path": "logs/app.log"},
                    "error_file": {"enabled": True, "level": "ERROR", "path": "logs/error.log"}
                }
            }
        }
        
        self.migration_log.append("新配置结构创建完成")
        return new_config
    
    def _extract_env_variables(self, old_settings) -> Dict[str, str]:
        """提取环境变量"""
        self.migration_log.append("开始提取环境变量")
        
        env_vars = {
            # 基础配置
            "ENV": "dev",
            "DEBUG": "false",
            "SECRET_KEY": "your-secret-key-at-least-32-characters-long",
            
            # 数据库配置 - 需要从旧配置中提取
            "MYSQL_HOST": "localhost",
            "MYSQL_PORT": "3306",
            "MYSQL_USER": "your_mysql_user",
            "MYSQL_PASSWORD": "your_mysql_password",
            "MYSQL_DATABASE": "ym_rag",
            
            "REDIS_HOST": "localhost",
            "REDIS_PORT": "6379",
            "REDIS_PASSWORD": "your_redis_password",
            "REDIS_DB": "0",
            
            "MILVUS_HOST": "localhost",
            "MILVUS_PORT": "19530",
            "MILVUS_USER": "root",
            "MILVUS_PASSWORD": "your_milvus_password",
            "MILVUS_DATABASE": "YM",
            
            # AI配置
            "LLM_BASE_URL": "https://api.openai.com/v1",
            "LLM_API_KEY": "your_openai_api_key",
            "LLM_MODEL": "gpt-3.5-turbo",
            
            "EMBEDDING_MODEL": "text-embedding-ada-002",
            "EMBEDDING_MODEL_PATH": "/path/to/local/model",
            
            # 第三方服务
            "DINGTALK_CLIENT_ID": "your_dingtalk_client_id",
            "DINGTALK_CLIENT_SECRET": "your_dingtalk_client_secret",
            
            "GOOGLE_API_KEY": "your_google_api_key",
            "TAVILY_API_KEY": "your_tavily_api_key",
            
            "LANGFUSE_PUBLIC_KEY": "your_langfuse_public_key",
            "LANGFUSE_SECRET_KEY": "your_langfuse_secret_key",
            "LANGFUSE_HOST": "https://cloud.langfuse.com",
            
            # 日志配置
            "LOG_LEVEL": "INFO"
        }
        
        # 尝试从旧配置中提取实际值
        try:
            if hasattr(old_settings, 'database'):
                db_config = old_settings.database
                if hasattr(db_config, 'REDIS_CONFIG'):
                    redis_config = db_config.REDIS_CONFIG.get('dev', {})
                    if 'url' in redis_config:
                        # 解析Redis URL
                        redis_url = redis_config['url']
                        # 这里可以添加URL解析逻辑
                        self.migration_log.append(f"发现Redis配置: {redis_url[:20]}...")
        except Exception as e:
            self.migration_log.append(f"提取旧配置值时出错: {str(e)}")
        
        self.migration_log.append("环境变量提取完成")
        return env_vars
    
    def _save_new_config(self, config: Dict[str, Any]):
        """保存新配置文件"""
        config_file = self.new_config_dir / "config.yaml"
        self.new_config_dir.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        self.migration_log.append(f"新配置文件已保存: {config_file}")
    
    def _save_env_file(self, env_vars: Dict[str, str]):
        """保存环境变量文件"""
        env_file = self.new_config_dir / ".env.example"
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write("# 环境变量配置模板\n")
            f.write("# 复制此文件为 .env 并填入实际值\n\n")
            
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        self.migration_log.append(f"环境变量模板已保存: {env_file}")
    
    def _backup_old_config(self):
        """备份旧配置"""
        backup_dir = self.new_config_dir / "backup"
        backup_dir.mkdir(exist_ok=True)
        
        if self.old_config_path.exists():
            backup_file = backup_dir / f"old_{self.old_config_path.name}"
            shutil.copy2(self.old_config_path, backup_file)
            self.migration_log.append(f"旧配置已备份: {backup_file}")


def main():
    """主函数 - 执行配置迁移"""
    print("🚀 开始配置迁移")
    
    # 配置路径
    old_config_path = "main/config/config_center.py"
    new_config_dir = "main/config"
    
    # 创建迁移器
    migrator = ConfigMigrator(old_config_path, new_config_dir)
    
    # 执行迁移
    result = migrator.migrate_from_old_config()
    
    if result["status"] == "success":
        print("✅ 配置迁移成功!")
        print("\n📋 迁移日志:")
        for log in result["migration_log"]:
            print(f"  - {log}")
        
        print(f"\n🔧 需要设置的环境变量: {len(result['env_vars'])}个")
        print("📁 请查看 .env.example 文件了解详情")
        
    else:
        print("❌ 配置迁移失败!")
        print(f"错误: {result['error']}")


if __name__ == "__main__":
    main()
