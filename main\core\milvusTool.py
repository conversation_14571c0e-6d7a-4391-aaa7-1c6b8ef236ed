#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: milvusTool.py 
@time: 2024/10/21 15:31
@Description: 
"""
from functools import lru_cache

from langchain_milvus import Milvus

from const.custom_exception import MilvusError
from core.myEmb import init_embedding
from config.config_center import settings
from common.myLog import logger

logger.name = __name__


class MilvusService:

    def __init__(self):
        self.embedding = init_embedding()
        self.connection = settings.database.MILVUS_DB

    def add_milvus_data(self, collection_name: str, datas: list) -> None:
        """
        :param collection_name:
        :param datas:
        :return:
        """
        Milvus.from_documents(
                    documents=datas,
                    embedding=self.embedding,
                    connection_args=self.connection,
                    collection_name=collection_name
                )

    def initialize_milvus(self, collection_name: str) -> Milvus:
        """
        :param collection_name:
        :return:
        """
        try:
            return Milvus(
                embedding_function=self.embedding,
                connection_args=self.connection,
                collection_name=collection_name
            )
        except Exception as e:
            logger.error(f"向量存储初始化失败: {str(e)}")
            raise MilvusError(f"向量存储初始化失败: {str(e)}")

@lru_cache(maxsize=settings.MAX_SIZE)
def init_milvus():
    return MilvusService()