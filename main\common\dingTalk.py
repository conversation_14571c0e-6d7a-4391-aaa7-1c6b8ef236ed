#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: dingTalk.py
@time: 2024/10/9 16:01
@Description:
"""
from urllib.parse import urljoin
from typing import Optional, Dict, Any

import requests
from requests.exceptions import RequestException
from tenacity import retry, stop_after_attempt, wait_exponential

from config.config_center import settings
from common.myLog import logger
from const.custom_exception import DingTalkAPIError

logger.name = __name__

class DingTalkAPI:
    
    def __init__(self, auth_code: str):
        self._api_url = settings.dingtalk.api_url.rstrip('/')
        self._oapi_url = settings.dingtalk.oapi_url.rstrip('/')
        self._client_id = settings.dingtalk.clientId
        self._client_secret = settings.dingtalk.clientSecret
        self._auth_code = auth_code
        
        self._session = requests.Session()
        self._session.headers.update({'Content-Type': 'application/json'})
        
        self._access_token: Optional[str] = None
        self._oaccess_token: Optional[str] = None
        
        self._initialize_tokens()

    def _initialize_tokens(self) -> None:
        self._access_token = self._get_access_token()
        self._oaccess_token = self._get_oaccess_token()

    @retry(stop=stop_after_attempt(1), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        try:
            response = self._session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            logger.error(f"DingTalk API request failed: {str(e)}")
            raise DingTalkAPIError(f"API request failed: {str(e)}") from e

    def _get_access_token(self) -> Optional[str]:
        url = urljoin(self._api_url, '/v1.0/oauth2/userAccessToken')
        data = {
            "clientId": self._client_id,
            "clientSecret": self._client_secret,
            "code": self._auth_code,
            "grantType": "authorization_code"
        }
        response = self._make_request('POST', url, json=data)
        return response.get('accessToken')

    def _get_oaccess_token(self) -> Optional[str]:
        url = urljoin(self._oapi_url, '/gettoken')
        params = {
            "appkey": self._client_id,
            "appsecret": self._client_secret
        }
        
        response = self._make_request('GET', url, params=params)
        return response.get('access_token')

    def get_user_mobile(self) -> Optional[str]:
        if not self._access_token:
            raise DingTalkAPIError("Access token not available")
            
        url = urljoin(self._api_url, '/v1.0/contact/users/me')
        headers = {"x-acs-dingtalk-access-token": self._access_token}
        
        response = self._make_request('GET', url, headers=headers)
        return response.get('mobile')

    def get_user_id(self) -> Optional[str]:
        if not self._oaccess_token:
            raise DingTalkAPIError("OAccess token not available")
            
        mobile = self.get_user_mobile()
        if not mobile:
            raise DingTalkAPIError("User mobile not available")
            
        url = urljoin(self._oapi_url, f'/topapi/v2/user/getbymobile')
        params = {'access_token': self._oaccess_token}
        data = {'mobile': mobile}
        
        response = self._make_request('POST', url, params=params, json=data)
        return response.get('result', {}).get('userid')

    def get_user_info(self) -> Dict[str, Any]:
        if not self._oaccess_token:
            raise DingTalkAPIError("OAccess token not available")
            
        user_id = self.get_user_id()
        if not user_id:
            raise DingTalkAPIError("User ID not available")
            
        url = urljoin(self._oapi_url, f'/topapi/v2/user/get')
        params = {'access_token': self._oaccess_token}
        data = {
            'userid': user_id,
            'language': 'zh_CN'
        }
        
        response = self._make_request('POST', url, params=params, json=data)
        result = response.get('result', {})
        
        is_leader = any(item.get('leader') for item in result.get('leader_in_dept', []))
        
        return {
            'dt_userid': result.get('userid'),
            'dt_name': result.get('name'),
            'dt_mobile': result.get('mobile'),
            'dt_title': result.get('title'),
            'dt_unionid': result.get('unionid'),
            'is_admin': is_leader,
            'is_active': result.get('active'),
            'roles_id': "3" if is_leader else "1"
        }