#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: llm.py 
@time: 2025/1/23 9:54
@Description: 
"""
import os
from functools import lru_cache

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

from config.config_center import settings
from common.myLog import logger

load_dotenv()
logger.name = __name__

@lru_cache(maxsize=settings.MAX_SIZE)
def getLlm(stream: bool = True) -> ChatOpenAI:
    return ChatOpenAI(
        openai_api_base=os.environ.get("BASE_URL"),
        openai_api_key=os.environ.get("API_KEY"),
        model_name=os.environ.get("MODEL"),
        temperature=0.5,
        streaming=stream
    )


class LLMFactory:
    """LLM工厂类"""

    @staticmethod
    def create_llm(
            session_state=None,
            stream: bool = True
    ) -> ChatOpenAI:
        # 使用传入的 session_state 或默认值
        llm_config = session_state.get("llm", {}) if session_state else {}
        temperature = session_state.get("temperature", 0.1) if session_state else 0.1
        max_tokens = session_state.get("max_token", 0) if session_state.get("max_token", 0) else None

        llm = ChatOpenAI(
            openai_api_base=llm_config.get("base_url", os.environ.get("BASE_URL")),
            openai_api_key=llm_config.get("api_key", os.environ.get("API_KEY")),
            model_name=llm_config.get("model", os.environ.get("MODEL")),
            streaming=stream,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return llm