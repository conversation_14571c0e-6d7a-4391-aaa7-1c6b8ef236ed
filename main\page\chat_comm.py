#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: chat_comm.py
@time: 2024/10/14 16:24
@Description: 
"""
import time

import streamlit as st

from common.utils import stream_output_com, remove_empty_lines
from config.config_center import settings
from const.enums import QueryRuleEnum
from agent.NaiveRag import init_chain_common


async def init_chat_comm(active: str):
    st.markdown(
        f"""### {settings.business.ROLES.get(active)}<span style='color: {settings.ui.global_color};'>智能</span>问答助手""",
        unsafe_allow_html=True
    )

    st.divider()

    st.session_state.active = active

    if active != st.session_state.last_active:
        del st.session_state.history_messages
        del st.session_state.last_active

    if "history_messages" not in st.session_state:
        st.session_state.history_messages = [
            {
                "role": "assistant",
                "avatar": settings.paths.SYSTEM_LOG0_PATH,
                "content": settings.business.Q_TEMPLATE.get(active)
            }
        ]

    for message in st.session_state.history_messages:
        with st.chat_message(message["role"], avatar=message.get("avatar", None)):
            st.markdown(message["content"])

    if prompt := st.chat_input("请输入你的问题，按Enter键发送。"):
        msg = remove_empty_lines(prompt)
        with st.chat_message("user"):
            st.session_state.history_messages.append({"role": "user", "content": msg})
            st.markdown(remove_empty_lines(msg))
            st.session_state.curd.add_message(
                role=QueryRuleEnum.user.value,
                content=msg,
                dt_userid=st.session_state.userInfo["dt_userid"],
                dt_name=st.session_state.userInfo["dt_name"],
                type=active
            )

        with st.chat_message("assistant", avatar=settings.paths.SYSTEM_LOG0_PATH):
            with st.spinner("请稍等，约苗君思考中..."):
                start_time = time.time()
                llm = init_chain_common(
                    max_tokens=st.session_state.max_token,
                    temperature=st.session_state.temperature,
                )
                rps = stream_output_com(
                    model=llm,
                    input_message=msg,
                    session_id=st.session_state.userInfo["dt_userid"],
                    ai_type="TSL"
                )
                response = st.write_stream(rps)
                end_time = time.time()
                query_time = round(end_time - start_time, 2)
                st.caption(f"⏱️耗时：{query_time} second(s)")
            st.session_state.history_messages.append(
                {
                    "role": "assistant",
                    "avatar": settings.paths.SYSTEM_LOG0_PATH,
                    "content": response
                }
            )
            st.session_state.last_active = active
            st.session_state.curd.add_message(
                role=QueryRuleEnum.ai.value,
                content=response,
                dt_userid="",
                dt_name="",
                type=active
            )
