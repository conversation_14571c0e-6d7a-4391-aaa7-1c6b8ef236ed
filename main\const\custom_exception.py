#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: custom_exception.py 
@time: 2025/2/17 10:22
@Description: 
"""

class BaseError(Exception):
    """基础异常类"""
    pass

class ConfigError(BaseError):
    """配置相关错误"""
    pass

class FileProcessError(BaseError):
    """文件处理相关错误"""
    pass

class RetrieverError(BaseError):
    """检索器相关错误"""
    pass

class DingTalkAPIError(BaseError):
    """钉钉API错误"""
    pass

class EmbeddingError(BaseError):
    """嵌入模型连接错误"""
    pass

class MilvusError(BaseError):
    """milvus数据库连接错误"""
    pass
