#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: chian.py 
@time: 2024/9/25 15:10
@Description: 
"""
import os
from functools import lru_cache

from dotenv import load_dotenv
from typing import Union

import streamlit as st

from langfuse import Lang<PERSON>
from langchain_chroma import Chroma
from langchain_openai import ChatOpenAI
from langchain.globals import set_llm_cache
from langchain_core.documents import Document
from langchain_community.cache import SQLiteCache
from BCEmbedding.tools.langchain import BCERerank
from langchain_core.output_parsers import StrOutputParser
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_core.chat_history import BaseChatMessageHistory
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders.csv_loader import CSVLoader
from langchain.retrievers.document_compressors import LL<PERSON>hainExtractor
from langchain.retrievers import ContextualCompression<PERSON><PERSON>riever, MultiQueryRetriever
from langchain_core.prompts import PromptTemplate, MessagesPlaceholder, ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, Runnable, RunnableWithMessageHistory
from langchain_community.document_loaders import PyPDFLoader, TextLoader, UnstructuredMarkdownLoader
from langchain_community.chat_message_histories import RedisChatMessageHistory, SQLChatMessageHistory

from config.config_center import settings


load_dotenv()

base_url = os.environ.get("BASE_URL")
api_key = os.environ.get("API_KEY")
model = os.environ.get("MODEL")

set_llm_cache(SQLiteCache(database_path=settings.paths.SQl_CACHE_PATH))


def get_redis_history(
        session_id: str
) -> BaseChatMessageHistory:
    return RedisChatMessageHistory(
        session_id=session_id,
        url=settings.database.REDIS_URL,
        key_prefix=settings.business.MEMORY_KEY,
        ttl=settings.TIME_OUT
    )


def get_sqlite_history(
        session_id: str
) -> BaseChatMessageHistory:
    return SQLChatMessageHistory(
        session_id=session_id,
        connection_string=settings.paths.HISTORY_DB_PATH
    )


@st.cache_data(ttl=settings.TIME_OUT)
def getPrompt(
        name: str
) -> PromptTemplate:
    langfuse = Langfuse()
    langfuse_prompt = langfuse.get_prompt(name)
    try:
        prompt = langfuse_prompt.get_langchain_prompt()
    except:
        prompt = settings.prompt.rag
    return prompt


@st.cache_data(ttl=settings.TIME_OUT)
def history_prompt(
        name: str
) -> ChatPromptTemplate:
    langfuse = Langfuse()
    langfuse_prompt = langfuse.get_prompt(name)
    try:
        prompt = langfuse_prompt.get_langchain_prompt()
    except:
        prompt = settings.prompt.rag
    return ChatPromptTemplate.from_messages(
        [
            ("system", "你是约苗智能问答助手，你的任务是根据下述给定的已知信息回答用户问题。"),
            MessagesPlaceholder(variable_name="history"),
            ("human", prompt)
        ]
    )

def chunk_text(
        filePath: Union[str, list],
        chunk_size: int = 500,
        chunk_overlap: int = 200
) -> list:
    loaders = {
        "txt": TextLoader,
        "pdf": PyPDFLoader,
        "csv": CSVLoader,
        "md": UnstructuredMarkdownLoader,
        "docx": TextLoader
    }
    file_extension = filePath.split(".")[-1]
    loader_class = loaders.get(file_extension)
    if loader_class:
        try:
            if file_extension == "pdf":
                loader = loader_class(filePath, extract_images=True)
            elif file_extension == "md":
                loader = loader_class(filePath, mode='elements', encoding="utf-8")
            else:
                loader = loader_class(filePath, encoding="utf-8")
            text = loader.load()
        except Exception as e:
            print(f"文本加载失败:{file_extension} files:{e}")
        else:
            try:
                text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
                docs = text_splitter.split_documents(text)
                if file_extension == "md":
                    docs = [Document(metadata={"source": i.metadata.get("source")}, page_content=i.page_content) for i in docs]
            except Exception as e:
                print(f"文本切割失败:{file_extension} files:{e}")
            else:
                return docs
    else:
        print(f"本文件后缀不支持: {file_extension}")
        return None


@lru_cache(maxsize=settings.MAX_SIZE)
def init_embedding():
    from core.myEmb import MyEmbeddings
    embedding = MyEmbeddings()
    return embedding


def init_Chroma(
        filePath: Union[str, list],
        chunk_size: int = 500,
        chunk_overlap: int = 200
) -> Chroma:
    chunk_data = chunk_text(filePath, chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    ChromaData = Chroma.from_documents(
        persist_directory=settings.paths.CHROMADB_DIRECTORY,
        collection_name="questions",
        embedding=init_embedding(),
        documents=chunk_data
    )
    return ChromaData


def init_vectorstore():
    vectorstore = Chroma(
        persist_directory=settings.paths.CHROMADB_DIRECTORY,
        collection_name="questions",
        embedding_function=init_embedding()
    )
    return vectorstore

@lru_cache(maxsize=settings.MAX_SIZE)
def init_vectorstore_milvus(
        collection_name: str
):
    from core.milvusTool import init_milvus
    return init_milvus().initialize_milvus(collection_name)


def MultipleRetrievalFuc(
        retriever: VectorStoreRetriever,
        max_tokens: int,
        temperature: float
) -> MultiQueryRetriever:
    return MultiQueryRetriever.from_llm(
        retriever=retriever,
        llm=init_llm(max_tokens, temperature)
    )


def ContextualCompressionRetrieverFuc(
        retriever: VectorStoreRetriever,
        max_tokens: int,
        temperature: float
) -> ContextualCompressionRetriever:
    compressor = LLMChainExtractor.from_llm(
        llm=init_llm(max_tokens, temperature),
    )
    retriever = ContextualCompressionRetriever(
        base_retriever=retriever,
        base_compressor=compressor
    )
    return retriever


def RerankRetrieverFuc(
        reranker_model: str,
        retriever: VectorStoreRetriever,
        k: int
) -> ContextualCompressionRetriever:
    CUDA = False
    reranker_args = {'model': reranker_model, 'top_n': k, 'device': "cuda:0" if CUDA else "cpu"}
    reranker = BCERerank(**reranker_args)
    compression_retriever = ContextualCompressionRetriever(
        base_compressor=reranker, base_retriever=retriever
    )
    return compression_retriever


def init_retrieval(
        vectorstore: Chroma,
        searchType: str,
        max_tokens: int,
        temperature: float,
) -> VectorStoreRetriever | MultiQueryRetriever | ContextualCompressionRetriever:
    SEARCH_TYPE: str = st.session_state.search_type

    BK: int = st.session_state.base_k

    TK: int = st.session_state.top_k

    BaseRetrieval = vectorstore.as_retriever(
        search_type=SEARCH_TYPE,
        search_kwargs={"k": int(BK)}
    )
    match searchType:
        case "Base":
            return BaseRetrieval
        case "MultiQuery":
            return MultipleRetrievalFuc(BaseRetrieval, max_tokens, temperature)
        case "Compression":
            return ContextualCompressionRetrieverFuc(BaseRetrieval, max_tokens, temperature)
        case "Rerank":
            return RerankRetrieverFuc(settings.paths.RERANK_MODEL_PATH, BaseRetrieval, int(TK))


def init_prompt():
    prompt = PromptTemplate(
        input_variables=["query", "context"],
        template=getPrompt("rag")
    )
    return prompt


def init_prompt_comm():
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a helpful assistant"),
        MessagesPlaceholder(variable_name="history"),
        ("human", "{question}"),
    ])
    return prompt


def init_llm(
        max_tokens: int,
        temperature: float
) -> ChatOpenAI:
    LLM_CONFIG: dict = st.session_state.llm
    return ChatOpenAI(
        openai_api_base=LLM_CONFIG.get("base_url", base_url),
        openai_api_key=LLM_CONFIG.get("api_key", api_key),
        model_name=LLM_CONFIG.get("model", model),
        streaming=True,
        temperature=temperature,
        max_tokens=max_tokens
    )


def init_llm_comm(
        max_tokens: int,
        temperature: float,
) -> ChatOpenAI:
    LLM_CONFIG_COMM: dict = st.session_state.llm_comm
    return ChatOpenAI(
        openai_api_base=LLM_CONFIG_COMM.get("base_url", base_url),
        openai_api_key=LLM_CONFIG_COMM.get("api_key", api_key),
        model_name=LLM_CONFIG_COMM.get("model", model),
        streaming=True,
        temperature=temperature,
        max_tokens=max_tokens
    )


def format_docs(docs):
    return "\n\n".join(doc.page_content for doc in docs)


@st.cache_resource(ttl=settings.TIME_OUT)
def init_chain(
        searchType: str,
        collection_name: str,
        max_tokens: int,
        temperature: float
) -> Runnable:
    llm = init_llm(max_tokens, temperature)
    prompt_text = init_prompt()
    vectorstore = init_vectorstore_milvus(collection_name)
    retriever_example = init_retrieval(vectorstore, searchType, max_tokens, temperature)
    chain = (
            {"context": retriever_example | format_docs, "query": RunnablePassthrough()}
            | prompt_text
            | llm
            | StrOutputParser()
    )
    return chain


@st.cache_resource(ttl=settings.TIME_OUT)
def init_chain_common(
        max_tokens: int,
        temperature: float
):
    llm = init_llm_comm(max_tokens, temperature)
    prompt_text = init_prompt_comm()
    chain = prompt_text | llm | StrOutputParser()
    chain_with_history = RunnableWithMessageHistory(
        chain,
        get_redis_history,
        input_messages_key="question",
        history_messages_key="history",
    )
    return chain_with_history


@st.cache_resource(ttl=settings.TIME_OUT)
def init_Retrieval_chain(
        max_tokens: int,
        temperature: float
):
    llm = init_llm(max_tokens, temperature)
    prompt = history_prompt('rag')
    chain = prompt | llm | StrOutputParser()
    with_message_history = RunnableWithMessageHistory(
        chain,
        get_redis_history,
        input_messages_key="query",
        history_messages_key="history",
    )
    return with_message_history


@st.cache_resource(ttl=settings.TIME_OUT)
def init_vectorstore_retriever(
        collection_name: str,
        **kwargs
) -> VectorStoreRetriever:
    vectorstore = init_vectorstore_milvus(collection_name)
    retriever = init_retrieval(
        vectorstore,
        **kwargs
    )
    return retriever