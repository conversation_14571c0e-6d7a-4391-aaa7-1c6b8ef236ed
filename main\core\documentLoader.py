#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: documentLoader.py 
@time: 2025/2/17 10:07
@Description: 
"""
import asyncio
import os
from typing import List, Dict, Any

from langchain_community.document_loaders import TextLoader, PyPDFLoader, CSVLoader, UnstructuredMarkdownLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from const.custom_exception import FileProcessError

# 文件大小限制
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
# 支持的文件类型
SUPPORTED_FILE_TYPES = {"txt", "pdf", "csv", "md", "docx"}


class DocumentLoader:
    """文档加载器类"""

    def __init__(self):
        self.loaders = {
            "txt": TextLoader,
            "pdf": <PERSON>yPD<PERSON>oader,
            "csv": CSVLoader,
            "md": UnstructuredMarkdownLoader,
            "docx": TextLoader
        }

    def load_document(self, file_path: str) -> List[Document]:
        """加载文档"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        if os.path.getsize(file_path) > MAX_FILE_SIZE:
            raise ValueError(f"文件过大: {file_path}")

        file_extension = file_path.split(".")[-1].lower()
        if file_extension not in self.loaders:
            raise ValueError(f"不支持的文件类型: {file_extension}")

        loader_class = self.loaders[file_extension]
        try:
            loader = self._create_loader(loader_class, file_path, file_extension)
            return loader.load()
        except Exception as e:
            raise FileProcessError(f"文件加载失败: {str(e)}")

    @staticmethod
    def _create_loader(loader_class, file_path: str, file_extension: str):
        if file_extension == "pdf":
            return loader_class(file_path, extract_images=True)
        elif file_extension == "md":
            return loader_class(file_path, mode='elements', encoding="utf-8")
        return loader_class(file_path, encoding="utf-8")


class TextSplitter:
    """文本分割器类"""

    @staticmethod
    def split_documents(
            documents: List[Document],
            chunk_size: int = 500,
            chunk_overlap: int = 200,
            file_extension: str = None
    ) -> List[Document]:
        try:
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            docs = splitter.split_documents(documents)
            if file_extension == "md":
                docs = [Document(metadata={"source": i.metadata.get("source")}, page_content=i.page_content) for i in docs]
            return docs
        except Exception as e:
            raise FileProcessError(f"文本分割失败: {str(e)}")


async def loader_document(file_path: str) -> List[Document]:
    loader = DocumentLoader()
    documents = await asyncio.to_thread(loader.load_document, file_path)
    return documents


async def process_document_async(file_path: str, chunk_size: int, chunk_overlap: int):
    """异步文档处理"""
    documents = await loader_document(file_path)
    splitter = TextSplitter()
    file_extension = file_path.split('.')[-1].lower()
    chunks = await asyncio.to_thread(splitter.split_documents, documents, chunk_size, chunk_overlap, file_extension)
    return chunks


def validate_file_path(file_path: str) -> bool:
    """验证文件路径"""
    if not os.path.exists(file_path):
        return False

    file_extension = file_path.split(".")[-1].lower()
    return file_extension in SUPPORTED_FILE_TYPES


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    return {
        "size": os.path.getsize(file_path),
        "type": file_path.split(".")[-1].lower(),
        "modified": os.path.getmtime(file_path)
    }
