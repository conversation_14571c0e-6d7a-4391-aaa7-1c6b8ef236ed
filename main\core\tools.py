#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: tools.py 
@time: 2025/2/17 16:26
@Description: 
"""
import os
os.environ["TAVILY_API_KEY"] = '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'

from dotenv import load_dotenv
from langchain_community.tools import TavilySearchResults
from langchain_community.utilities import SerpAPIWrapper
from tenacity import retry, stop_after_attempt, wait_exponential

from common.myLog import logger

load_dotenv()

logger.name = __name__

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def GoogleSearchResultsTool(
        question: str
) -> str:
    try:
        return SerpAPIWrapper(
                serpapi_api_key=os.getenv("GOOGLE_API_KEY"),
                params={
                    "gl":"cn",
                    "hl":"zh-CN",
                }
        ).run(question)
    except Exception as e:
        logger.error(f"Google 搜索失败: {str(e)}")
        return "搜索服务暂时不可用，请稍后重试。"


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def TavilySearchResultsTool(
        question: str,
        max_results: int = 10
) -> str:
    try:
        results = TavilySearchResults(
            max_results=max_results,
        ).run(question)
        return ''.join([i["content"] for i in results])
    except Exception as e:
        logger.error(f"Tavily 搜索失败: {str(e)}")
        return "搜索服务暂时不可用，请稍后重试。"
