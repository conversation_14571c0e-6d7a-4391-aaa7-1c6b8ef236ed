#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: db_optimizer.py
@time: 2025/7/24
@Description: 数据库连接优化
"""
import time
from functools import lru_cache
from contextlib import contextmanager
from typing import Optional, Dict, Any

import streamlit as st
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool, StaticPool
from sqlalchemy.engine import Engine

from common.myLog import logger
from config.config_center import settings


class OptimizedDatabaseManager:
    """优化的数据库管理器"""
    
    def __init__(self):
        self._engine: Optional[Engine] = None
        self._session_factory = None
        self._connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "connection_errors": 0,
            "avg_connection_time": 0.0
        }
    
    @property
    def engine(self) -> Engine:
        """获取数据库引擎（懒加载）"""
        if self._engine is None:
            self._engine = self._create_optimized_engine()
        return self._engine
    
    def _create_optimized_engine(self) -> Engine:
        """创建优化的数据库引擎"""
        # 根据环境调整连接池配置
        if settings.env == "prod":
            pool_config = {
                "poolclass": QueuePool,
                "pool_size": 20,
                "max_overflow": 30,
                "pool_timeout": 30,
                "pool_recycle": 3600,  # 1小时回收连接
                "pool_pre_ping": True,
            }
        else:
            pool_config = {
                "poolclass": QueuePool,
                "pool_size": 10,
                "max_overflow": 15,
                "pool_timeout": 20,
                "pool_recycle": 1800,  # 30分钟回收连接
                "pool_pre_ping": True,
            }
        
        engine = create_engine(
            settings.database.SQLALCHEMY_DATABASE_URI,
            echo=False,
            **pool_config
        )
        
        # 添加连接事件监听器
        self._setup_connection_events(engine)
        
        # 预热连接池
        self._warmup_connections(engine)
        
        logger.info(f"数据库引擎创建完成，连接池配置: {pool_config}")
        return engine
    
    def _setup_connection_events(self, engine: Engine):
        """设置连接事件监听器"""
        @event.listens_for(engine, "connect")
        def receive_connect(dbapi_connection, connection_record):
            self._connection_stats["total_connections"] += 1
            connection_record.info['connect_time'] = time.time()
        
        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            self._connection_stats["active_connections"] += 1
        
        @event.listens_for(engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            self._connection_stats["active_connections"] -= 1
            if 'connect_time' in connection_record.info:
                connection_time = time.time() - connection_record.info['connect_time']
                # 更新平均连接时间
                current_avg = self._connection_stats["avg_connection_time"]
                total_connections = self._connection_stats["total_connections"]
                self._connection_stats["avg_connection_time"] = (
                    (current_avg * (total_connections - 1) + connection_time) / total_connections
                )
    
    def _warmup_connections(self, engine: Engine, num_connections: int = 3):
        """预热连接池"""
        try:
            logger.info(f"开始预热数据库连接池，预热连接数: {num_connections}")
            start_time = time.time()
            
            connections = []
            for i in range(num_connections):
                conn = engine.connect()
                # 执行简单查询验证连接
                conn.execute("SELECT 1")
                connections.append(conn)
            
            # 关闭预热连接
            for conn in connections:
                conn.close()
            
            warmup_time = time.time() - start_time
            logger.info(f"数据库连接池预热完成，耗时: {warmup_time:.2f}s")
            
        except Exception as e:
            logger.error(f"数据库连接池预热失败: {str(e)}")
            self._connection_stats["connection_errors"] += 1
    
    @property
    def session_factory(self):
        """获取会话工厂"""
        if self._session_factory is None:
            self._session_factory = scoped_session(
                sessionmaker(bind=self.engine, expire_on_commit=False)
            )
        return self._session_factory
    
    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        pool = self.engine.pool
        return {
            **self._connection_stats,
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            with self.get_session() as session:
                session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {str(e)}")
            return False


# 全局数据库管理器实例
@lru_cache(maxsize=1)
def get_db_manager() -> OptimizedDatabaseManager:
    """获取数据库管理器单例"""
    return OptimizedDatabaseManager()


class OptimizedCRUD:
    """优化的CRUD操作类"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
    
    def _execute_with_retry(self, operation, max_retries: int = 3):
        """带重试的数据库操作"""
        for attempt in range(max_retries):
            try:
                with self.db_manager.get_session() as session:
                    return operation(session)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(f"数据库操作失败，重试 {attempt + 1}/{max_retries}: {str(e)}")
                time.sleep(0.1 * (attempt + 1))  # 指数退避
    
    def batch_operation(self, operations, batch_size: int = 100):
        """批量数据库操作"""
        with self.db_manager.get_session() as session:
            for i in range(0, len(operations), batch_size):
                batch = operations[i:i + batch_size]
                for operation in batch:
                    operation(session)
                session.flush()  # 刷新但不提交
            session.commit()  # 最后统一提交


@st.cache_resource(ttl=3600)
def get_optimized_crud():
    """获取优化的CRUD实例（缓存1小时）"""
    return OptimizedCRUD()


def init_optimized_database():
    """初始化优化的数据库连接"""
    start_time = time.time()
    
    try:
        db_manager = get_db_manager()
        
        # 健康检查
        if not db_manager.health_check():
            raise Exception("数据库健康检查失败")
        
        # 获取连接统计
        stats = db_manager.get_connection_stats()
        
        init_time = time.time() - start_time
        logger.info(f"优化数据库初始化完成，耗时: {init_time:.2f}s")
        logger.info(f"连接池状态: {stats}")
        
        return get_optimized_crud()
        
    except Exception as e:
        init_time = time.time() - start_time
        logger.error(f"优化数据库初始化失败，耗时: {init_time:.2f}s，错误: {str(e)}")
        raise
