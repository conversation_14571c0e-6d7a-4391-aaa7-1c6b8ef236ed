#!/usr/bin/env/ python
# -*- coding: utf-8 -*-

import asyncio
import threading
import weakref
from abc import ABC, abstractmethod
from functools import lru_cache
from typing import Dict, Any, Optional, Type
from threading import RLock

from langchain.globals import set_llm_cache
from langchain_community.cache import SQLiteCache
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, Runnable, RunnableWithMessageHistory

from common.utils import format_context
from common.myLog import logger
from core.llm import LLMFactory
from core.prompt import prompt_manager
from core.retriever import init_vectorstore_retriever
from config.config_center import settings
from common.cache_manager import AdvancedCacheManager

logger.name = __name__

# 设置LLM缓存
set_llm_cache(SQLiteCache(database_path=settings.paths.SQl_CACHE_PATH))


class ChainFactory:
    """Chain工厂类"""
    # 定义支持的链类型映射
    _CHAIN_TYPES: Dict[str, Type["BaseChain"]] = {
        "rag": "RagChain",
        "common": "CommonChain",
        "history": "HistoryChain",
        "router": "RouterChain"
    }

    @classmethod
    def create_chain(
        cls,
        chain_type: str,
        session_state: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Runnable:
        """创建指定类型的Chain"""
        try:
            # 动态获取chain类
            chain_class_name = cls._CHAIN_TYPES.get(chain_type)
            if not chain_class_name:
                raise ValueError(f"不支持的Chain类型: {chain_type}")
            
            # 获取实际的类对象
            chain_class = globals()[chain_class_name]
            
            # 构建并返回chain
            kwargs['session_state'] = session_state
            return chain_class(**kwargs).build()
        except Exception as e:
            logger.error(f"Chain工厂类初始化失败: {str(e)}")
            raise


class BaseChain(ABC):
    """Chain基类"""
    def __init__(
        self,
        session_state: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """初始化Chain基类"""
        self.llm = LLMFactory.create_llm(
            stream=True,
            session_state=session_state
        )
        self.session_state = session_state

    @abstractmethod
    def build(self) -> Runnable:
        """chain基础构造器"""
        pass


class RagChain(BaseChain):
    """RAG Chain实现"""
    # 全局检索器缓存管理器 - 使用高级缓存
    _retriever_cache_manager = AdvancedCacheManager(
        max_size=64,
        default_ttl=7200,  # 2小时过期
        cleanup_interval=600  # 10分钟清理一次
    )

    def __init__(
        self,
        retriever_type: str,
        collection_name: str,
        session_state: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """初始化RAG Chain"""
        super().__init__(session_state=session_state, **kwargs)
        self.retriever_type = retriever_type
        self.collection_name = collection_name
        self.prompt = prompt_manager.utils_prompt("rag_prompt")

    def _get_cached_retriever(self, retriever_type: str, collection_name: str) -> Any:
        """获取缓存的检索器"""
        cache_key = f"{retriever_type}_{collection_name}"

        def create_retriever():
            logger.info(f"初始化新的检索器: {cache_key}")
            retriever = init_vectorstore_retriever(
                retriever_type,
                collection_name,
                session_state=self.session_state
            )
            logger.info(f"检索器初始化成功: {cache_key}")
            return retriever

        return self._retriever_cache_manager.get_or_create(
            cache_key,
            create_retriever,
            ttl=7200  # 2小时过期
        )

    def build(self) -> Runnable:
        """构建RAG链"""
        retriever = self._get_cached_retriever(self.retriever_type, self.collection_name)

        chain = (
            RunnablePassthrough.assign(
                context=lambda x: format_context(retriever.invoke(x["question"])),
            )
            | self.prompt
            | self.llm
            | StrOutputParser()
        )
        return chain


class CommonChain(BaseChain):
    """通用Chain实现"""
    def __init__(
        self,
        prompt: ChatPromptTemplate,
        session_state: Optional[Dict[str, Any]] = None,
        parser: Optional[Any] = None,
        **kwargs
    ):
        """初始化通用Chain"""
        super().__init__(session_state=session_state, **kwargs)
        self.prompt = prompt
        self.parser = parser or StrOutputParser()

    def build(self) -> Runnable:
        """构建通用链"""
        return (
            self.prompt
            | self.llm
            | self.parser
        )


class HistoryChain(BaseChain):
    """记忆Chain实现"""
    def __init__(
        self, 
        prompt: ChatPromptTemplate, 
        session_state: Optional[Dict[str, Any]] = None, 
        **kwargs
    ):
        """初始化历史记忆Chain"""
        super().__init__(session_state=session_state, **kwargs)
        self.prompt = prompt

    def build(self) -> Runnable:
        """构建记忆链"""
        history_chain = self.prompt | self.llm | StrOutputParser()
        
        return RunnableWithMessageHistory(
            history_chain,
            self._create_redis_history,
            input_messages_key="question",
            history_messages_key="history",
        )

    @staticmethod
    def _create_redis_history(session_id: str) -> RedisChatMessageHistory:
        """创建Redis历史记录"""
        return RedisChatMessageHistory(
            session_id=session_id,
            url=settings.database.REDIS_URL,
            key_prefix=settings.business.MEMORY_KEY,
            ttl=3600
        )


class RouterChain(BaseChain):
    """智能路由决策器"""
    def __init__(
        self, 
        session_state: Optional[Dict[str, Any]] = None, 
        **kwargs
    ):
        """初始化路由Chain"""
        super().__init__(session_state=session_state, **kwargs)
        # 确保路由器使用流式输出
        self.llm = LLMFactory.create_llm(
            session_state=session_state,
            stream=True
        )
        self.prompt = prompt_manager.chat_template("router_prompt")

    def build(self) -> Runnable:
        """构建路由链"""
        chain = (
            self.prompt
            | self.llm
            | StrOutputParser()
            | self._normalize_output
        )
        
        return chain

    @staticmethod
    def _normalize_output(text: str) -> str:
        """标准化输出处理"""
        clean_text = text.strip().lower()
        
        # 定义规则字典，便于维护和扩展
        routing_rules = {
            "csq": "CSQ",
            "tq": "TQ",
            "aq": "AQ"
        }
        
        # 遍历检查匹配
        for key, value in routing_rules.items():
            if key in clean_text:
                return value
                
        # 默认返回CSQ
        return "CSQ"


class AsyncTools:
    """异步工具类"""

    @staticmethod
    async def init_chain_async(
            search_type: str,
            collection_name: str
    ) -> Runnable:
        """异步初始化 Chain"""
        try:
            # 并行执行初始化任务
            llm_task = asyncio.create_task(
                asyncio.to_thread(LLMFactory.create_llm)
            )
            prompt_task = asyncio.create_task(
                asyncio.to_thread(prompt_manager.utils_prompt, "rag")
            )
            retriever_task = asyncio.create_task(
                asyncio.to_thread(init_vectorstore_retriever, search_type, collection_name)
            )
            
            # 等待所有任务完成
            llm = await llm_task
            prompt = await prompt_task
            retriever = await retriever_task

            # 构建异步链
            async_chain = (
                {"context": retriever | format_context, "query": RunnablePassthrough()}
                | prompt
                | llm
                | StrOutputParser()
            )
            return async_chain
        except Exception as e:
            logger.error(f"Chain异步初始化失败: {str(e)}")
            raise


if __name__ == '__main__':
    pass