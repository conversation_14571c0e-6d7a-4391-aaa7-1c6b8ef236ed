#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: retriever.py 
@time: 2025/2/17 9:57
@Description: 
"""
from typing import Any, Dict, Optional
from functools import lru_cache

from BCEmbedding.tools.langchain import BCERerank
from langchain.retrievers import ContextualCompressionRetriever, MultiQueryRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor
from langchain_core.vectorstores import VectorStoreRetriever

from config.config_center import settings
from const.custom_exception import RetrieverError
from core.llm import LLMFactory
from core.milvusTool import init_milvus
from common.myLog import logger

logger.name = __name__


class RetrieverFactory:
    """检索器工厂类"""
    _retriever_cache: Dict[str, VectorStoreRetriever] = {}
    _llm_cache: Dict[str, Any] = {}
    _vectorstore_cache: Dict[str, Any] = {}
    
    @classmethod
    def _get_cached_llm(cls, llm_type: str = "default") -> Any:
        """获取缓存的LLM实例"""
        if llm_type not in cls._llm_cache:
            cls._llm_cache[llm_type] = LLMFactory.create_llm()
        return cls._llm_cache[llm_type]
    
    @classmethod
    def _get_cached_vectorstore(cls, collection_name: str) -> Any:
        """获取缓存的向量存储实例"""
        if collection_name not in cls._vectorstore_cache:
            cls._vectorstore_cache[collection_name] = init_milvus().initialize_milvus(collection_name)
        return cls._vectorstore_cache[collection_name]

    @classmethod
    def create_retriever(
            cls,
            retriever_type: str,
            collection_name: str,
            session_state: Optional[Dict] = None
    ) -> VectorStoreRetriever:
        """创建检索器（带缓存）"""
        try:
            # 生成缓存键
            cache_key = f"{retriever_type}_{collection_name}_{session_state.get('base_k', 3)}_{session_state.get('top_k', 3)}"
            
            # 检查缓存
            if cache_key in cls._retriever_cache:
                logger.info(f"使用缓存的检索器: {cache_key}")
                return cls._retriever_cache[cache_key]
            
            # 获取基础参数
            base_k = session_state.get('base_k', 3) if session_state else 3
            top_k = session_state.get('top_k', 3) if session_state else 3
            cuda_enabled = getattr(settings, 'CUDA_ENABLED', False)
            
            # 获取缓存的向量存储
            vectorstore = cls._get_cached_vectorstore(collection_name)
            
            # 创建基础检索器
            base_retriever = vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": int(base_k)}
            )

            # 定义检索器创建函数
            retrievers = {
                "Base": lambda: base_retriever,
                "MultiQuery": lambda: MultiQueryRetriever.from_llm(
                    retriever=base_retriever,
                    llm=cls._get_cached_llm()
                ),
                "Compression": lambda: ContextualCompressionRetriever(
                    base_retriever=base_retriever,
                    base_compressor=LLMChainExtractor.from_llm(
                        llm=cls._get_cached_llm()
                    )
                ),
                "Rerank": lambda: ContextualCompressionRetriever(
                    base_retriever=base_retriever,
                    base_compressor=BCERerank(
                        model=settings.paths.RERANK_MODEL_PATH,
                        top_n=int(top_k),
                        device="cuda:0" if cuda_enabled else "cpu"
                    )
                )
            }

            retriever_func = retrievers.get(retriever_type)
            if not retriever_func:
                raise ValueError(f"不支持的检索器类型: {retriever_type}")

            # 创建并缓存检索器
            retriever = retriever_func()
            cls._retriever_cache[cache_key] = retriever
            logger.info(f"创建新的检索器: {cache_key}")
            
            return retriever
            
        except Exception as e:
            raise RetrieverError(f"检索器创建失败: {str(e)}")

def init_vectorstore_retriever(
        retriever_type: str,
        collection_name: str,
        session_state=None
) -> VectorStoreRetriever:
    """初始化向量存储检索器"""
    try:
        return RetrieverFactory.create_retriever(
            retriever_type,
            collection_name,
            session_state=session_state
        )
    except Exception as e:
        logger.error(f"检索器初始化失败: {str(e)}")
        raise RetrieverError(f"检索器初始化失败: {str(e)}")
