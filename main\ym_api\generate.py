#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: generate.py
@time: 2025/1/22 10:10
@Description:
"""

import os
import re
import json
from typing import Optional

from fastapi import FastAPI, HTTPException, Header
from pydantic import BaseModel

from core.llm import getLlm
from core.prompt import prompt_manager

app = FastAPI()

ym_api_key = os.environ.get("YM_API_KEY")


class UserInput(BaseModel):
    question: str


class WishResponse(BaseModel):
    who: Optional[str] = None  # 为谁许愿
    job: Optional[str] = None  # 职业
    to_do: Optional[str] = None  # 变美范围


class ClearResponse(BaseModel):
    message: Optional[str] = None


@app.post("/analyze_wish")
async def analyze_wish(
        user_input: UserInput,
        x_api_key: str = Header(..., description="API访问密钥")
) -> WishResponse:
    # 验证API密钥
    if x_api_key != ym_api_key:
        raise HTTPException(
            status_code=403,
            detail="无权限访问，请提供有效的API密钥"
        )
    try:
        # 构建消息
        messages = prompt_manager.utils_prompt("ym_api").format_messages(user_question=user_input.question)

        try:
            # 获取AI响应
            response = await getLlm().ainvoke(messages)
            try:
                # 清理和规范化JSON字符串
                content = response.content.strip()

                # 尝试直接解析
                try:
                    result = json.loads(content)
                except json.JSONDecodeError:
                    pattern = r'"(\w+)":\s*"([^"]*)"'
                    matches = re.findall(pattern, content)

                    if matches:
                        # 构建新的字典
                        result = {key: value for key, value in matches}
                    else:
                        # 如果没有找到键值对，返回空响应
                        return WishResponse()

                # 验证结果格式
                if not isinstance(result, dict):
                    return WishResponse()

                # 规范化结果
                who = result.get("who")
                job = result.get("job")
                to_do = result.get("to_do")

                # 确保返回的是字符串或None，并处理特殊值
                def normalize_value(value):
                    if value is None or value.lower() == 'null' or value.strip() == '':
                        return None
                    return str(value)

                who = normalize_value(who)
                job = normalize_value(job)
                to_do = normalize_value(to_do)

                return WishResponse(
                    who=who,
                    job=job,
                    to_do=to_do
                )
            except Exception as e:
                return WishResponse()

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"AI服务调用失败: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器错误: {str(e)}"
        )