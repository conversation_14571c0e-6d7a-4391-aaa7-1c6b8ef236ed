#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: markdown_analysis.py
@time: 2025/5/26 16:06
@Description: 
"""
from typing import Dict, List, Any

from common.error_handler import ErrorHandler


class MarkdownAnalysis:
    """Markdown文档解析器"""

    def __init__(self, cases: List[Dict[str, Any]]):
        self.cases = cases

    @ErrorHandler.handle_json_error
    def create_markdown(self) -> str:
        """生成Markdown格式的测试用例文档"""
        modules = self._group_by_module()
        return self._generate_markdown_content(modules)

    def _group_by_module(self) -> Dict[str, List[Dict[str, Any]]]:
        """按模块分组测试用例"""
        modules = {}
        for case in self.cases:
            module = case['模块']
            if module not in modules:
                modules[module] = []
            modules[module].append(case)
        return modules

    def _generate_markdown_content(self, modules: Dict[str, List[Dict[str, Any]]]) -> str:
        """生成Markdown内容"""
        markdown = "# TestCase\n\n"
        for module, cases in modules.items():
            markdown += f"## {module}\n\n"
            for case in cases:
                markdown += self._generate_case_markdown(case)
        return markdown

    @staticmethod
    def _format_field(value: Any) -> str:
        if isinstance(value, list):
            if not value:
                return "  - 无  \n"
            return "".join(f"  - {item}  \n" if item else "  - 无  \n" for item in value)
        return f"  - {value}  \n" if value else "  - 无  \n"

    @staticmethod
    def _generate_case_markdown(case: Dict[str, Any]) -> str:
        """生成单个测试用例的Markdown内容"""
        markdown = f"### {case['用例标题']}\n\n"
        markdown += f"- **用例编号**: {case['用例编号']}  \n"
        markdown += f"- **优先级**: {case['优先级']}  \n"

        markdown += "- **前置条件**: \n"
        markdown += MarkdownAnalysis._format_field(case['前置条件'])

        markdown += "- **测试数据**: \n"
        markdown += MarkdownAnalysis._format_field(case['测试数据'])

        markdown += "- **操作步骤**: \n"
        markdown += MarkdownAnalysis._format_field(case['操作步骤'])

        markdown += "- **预期结果**: \n"
        markdown += MarkdownAnalysis._format_field(case['预期结果'])

        return markdown