#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: enums.py
@time: 2024/11/05 11:34
@Description:
"""
import enum
from enum import Enum

import streamlit_antd_components as sac


class BaseEnum(Enum):
    """枚举基类"""
    @classmethod
    def get_member_values(cls):
        return [item.value for item in cls._member_map_.values()]

    @classmethod
    def get_member_names(cls):
        return [name for name in cls._member_names_]


class IntEnum(int, BaseEnum):
    """整型枚举"""
    pass


class StrEnum(str, BaseEnum):
    """字符串枚举"""
    pass


class SatusEnum(IntEnum):
    y = 1  # 是
    n = 0  # 否


class QueryRuleEnum(StrEnum):
    user = "USER"  # 用户
    system = "SYSTEM" # 系统
    ai = "ASSISTANT" # 助手


class PromptType(Enum):
    """提示词类型枚举"""
    RAG = "rag_prompt"
    ROUTER = "router_prompt"
    SEARCH = "search_prompt"
    REQUIREMENT = "requirement_prompt"
    TECHNICAL = "technical_prompt"
    GENERAL = "general_prompt"
    CHAT = "chat_prompt"
    TESTCASE = "testcase_prompt"
    API = "ym_api"
    DEMAND_ANALYSIS= "demand_analysis"

class AiMenuEnum(enum.Enum):
    ASSISTANT_AI = ("3", sac.MenuItem("智能问答助手", icon='chat-dots'))
    DOCUMENT_AI = ("4", sac.MenuItem("文档分析助手", icon='book'))
    ANALYSIS_AI = ("10", sac.MenuItem("需求分析助手", icon='lightbulb'))
    TESTCASE_AI = ("5", sac.MenuItem("用例生成助手", icon='database'))

    @property
    def id(self):
        return self.value[0]

    @property
    def menu_item(self):
        return self.value[1]


class SystemMenuEnum:
    def __init__(
            self,
            auth_ids: list,
            auth: bool
    ):
        self.AiMenuItem: list = [item.menu_item for item in AiMenuEnum if item.id in auth_ids]
        self.Auth: bool = auth

    @property
    def _generate_menu(self):
        return {
            "1": sac.MenuItem('首页', icon='house-fill'),
            "2": sac.MenuItem('AI', icon='robot', children=self.AiMenuItem),
            "6": sac.MenuItem('知识库', icon='book-fill'),
            "7": sac.MenuItem('模型设置', icon='cpu-fill', disabled=not self.Auth),
            "8": sac.MenuItem('数据中心', icon='stack'),
            "9": sac.MenuItem('系统管理', icon='gear-fill', disabled=not self.Auth),
            "11": sac.MenuItem('缓存管理', icon='gear-fill', disabled=not self.Auth)
        }

    def __call__(self):
        return self._generate_menu