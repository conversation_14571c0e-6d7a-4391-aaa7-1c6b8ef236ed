#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: cache_manager.py
@time: 2025/7/23 
@Description: 高级缓存管理器
"""
import time
import threading
from typing import Any, Callable, Dict, Optional
from threading import RLock
from functools import wraps
from dataclasses import dataclass
from collections import OrderedDict

# from common.myLog import logger
import logging

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: float
    last_accessed: float
    access_count: int = 0
    ttl: Optional[float] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """更新访问时间"""
        self.last_accessed = time.time()
        self.access_count += 1


class AdvancedCacheManager:
    """高级缓存管理器 - 支持TTL、LRU、统计等功能"""
    
    def __init__(
        self, 
        max_size: int = 128, 
        default_ttl: Optional[float] = None,
        cleanup_interval: float = 300  # 5分钟清理一次
    ):
        self._lock = RLock()
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._cleanup_interval = cleanup_interval
        
        # 使用OrderedDict实现LRU
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        
        # 统计信息
        self._stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0
        }
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(
            target=self._periodic_cleanup, 
            daemon=True
        )
        self._cleanup_thread.start()
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._stats["misses"] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._stats["expirations"] += 1
                self._stats["misses"] += 1
                return None
            
            # 更新访问信息并移到末尾（LRU）
            entry.touch()
            self._cache.move_to_end(key)
            self._stats["hits"] += 1
            
            return entry.value
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[float] = None
    ) -> None:
        """设置缓存值"""
        with self._lock:
            current_time = time.time()
            
            # 使用默认TTL如果未指定
            if ttl is None:
                ttl = self._default_ttl
            
            entry = CacheEntry(
                value=value,
                created_at=current_time,
                last_accessed=current_time,
                ttl=ttl
            )
            
            # 如果key已存在，更新它
            if key in self._cache:
                self._cache[key] = entry
                self._cache.move_to_end(key)
            else:
                # 检查是否需要驱逐
                if len(self._cache) >= self._max_size:
                    self._evict_lru()
                
                self._cache[key] = entry
    
    def get_or_create(
        self, 
        key: str, 
        factory_func: Callable[[], Any],
        ttl: Optional[float] = None
    ) -> Any:
        """获取或创建缓存值"""
        # 先尝试获取
        value = self.get(key)
        if value is not None:
            return value
        
        # 创建新值
        try:
            new_value = factory_func()
            self.set(key, new_value, ttl)
            return new_value
        except Exception as e:
            logger.error(f"缓存工厂函数执行失败: {str(e)}")
            raise
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            logger.info("缓存已清空")
    
    def _evict_lru(self) -> None:
        """驱逐最近最少使用的项"""
        if self._cache:
            evicted_key, _ = self._cache.popitem(last=False)
            self._stats["evictions"] += 1
            logger.debug(f"驱逐缓存项: {evicted_key}")
    
    def _periodic_cleanup(self) -> None:
        """定期清理过期项"""
        while True:
            try:
                time.sleep(self._cleanup_interval)
                self._cleanup_expired()
            except Exception as e:
                logger.error(f"缓存清理线程错误: {str(e)}")
    
    def _cleanup_expired(self) -> None:
        """清理过期项"""
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats["expirations"] += 1
            
            if expired_keys:
                logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = (
                self._stats["hits"] / total_requests 
                if total_requests > 0 else 0
            )
            
            return {
                **self._stats,
                "cache_size": len(self._cache),
                "max_size": self._max_size,
                "hit_rate": hit_rate,
                "total_requests": total_requests
            }
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取详细的缓存信息"""
        with self._lock:
            cache_info = {}
            for key, entry in self._cache.items():
                cache_info[key] = {
                    "created_at": entry.created_at,
                    "last_accessed": entry.last_accessed,
                    "access_count": entry.access_count,
                    "ttl": entry.ttl,
                    "is_expired": entry.is_expired()
                }
            return cache_info
    
    def set_max_size(self, max_size: int) -> None:
        """设置缓存最大大小"""
        with self._lock:
            if max_size <= 0:
                raise ValueError("max_size 必须大于0")
            self._max_size = max_size
            
            # 如果当前缓存大小超过新的最大大小，则驱逐多余的项
            while len(self._cache) > self._max_size:
                self._evict_lru()
    
    def set_default_ttl(self, default_ttl: Optional[float]) -> None:
        """设置默认TTL"""
        with self._lock:
            self._default_ttl = default_ttl
    
    def set_cleanup_interval(self, cleanup_interval: float) -> None:
        """设置清理间隔"""
        with self._lock:
            if cleanup_interval <= 0:
                raise ValueError("cleanup_interval 必须大于0")
            self._cleanup_interval = cleanup_interval


def cached_method(
    ttl: Optional[float] = None,
    key_func: Optional[Callable] = None
):
    """缓存方法装饰器"""
    def decorator(func):
        cache = AdvancedCacheManager(default_ttl=ttl)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
            
            return cache.get_or_create(
                cache_key,
                lambda: func(*args, **kwargs),
                ttl
            )
        
        # 添加缓存管理方法
        wrapper.cache_clear = cache.clear
        wrapper.cache_stats = cache.get_stats
        wrapper.cache_info = cache.get_cache_info
        
        return wrapper
    return decorator


# 全局缓存实例
global_cache = AdvancedCacheManager(
    max_size=256,
    default_ttl=3600,  # 1小时
    cleanup_interval=300  # 5分钟
)


if __name__ == '__main__':
    global_cache.set_default_ttl(600)
    global_cache.get_or_create("test", lambda: "new value")
    global_cache.get_or_create("test1", lambda: "new value", ttl=400)
    global_cache.get_or_create("test1", lambda: "new value")
    print(global_cache.get_cache_info())