#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: style.py 
@time: 2024/11/12 11:26
@Description: 
"""

centered_css = """
    <style>                   
        .body-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 79vh;
            margin: -80px
        }
        a:hover {
            text-decoration: none;
        }        
        .st-emotion-cache-1rsyhoq a {
            color: white;
        }
        .bt-link {
            padding: 10px 10px;
            background-color: #4CAF50;
            font-size: 15px;
            font-weight: bold;
            text-decoration: none;
            border-radius: 5px;
        }
        .ym-container {
            margin-top:9%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom:-9%;
        }
        .st-emotion-cache-1rsyhoq p {
            display: none;
        }
    </style>
"""


index_css = """
    <style>
        .index-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 79vh;
            margin: -80px
        }
        .ym-container {
            margin-top:12%;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            margin-bottom:-12%;
        }
        a:hover {
            text-decoration: none;
        }
    </style>
"""


statistical_title_css = """
    <style>
        .statistical-title {
            text-align: center;
            color: red;
        }
    </style>
"""


ym_footer_css = """
    <style>
        .ym-footer {
            margin-top:120%;
            display: flex;
            align-items: flex-end;
        }
    </style>
"""