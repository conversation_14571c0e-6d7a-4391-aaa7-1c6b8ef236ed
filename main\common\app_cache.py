#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: app_cache.py
@time: 2025/7/24
@Description: 应用级缓存工具 - 基于高级缓存管理器的应用封装
"""
import hashlib
import json
from typing import Any, Dict, List, Optional, Callable
from functools import wraps

from main.common.cache_manager import AdvancedCacheManager, cached_method, global_cache
from main.common.cache_monitor import cache_manager as cache_monitor
from main.common.myLog import logger


class AppCacheManager:
    """应用缓存管理器 - 为不同业务场景提供专用缓存"""
    
    def __init__(self):
        # 用户相关缓存
        self.user_cache = AdvancedCacheManager(
            max_size=200,
            default_ttl=1800,  # 30分钟
            cleanup_interval=300
        )
        
        # 配置相关缓存
        self.config_cache = AdvancedCacheManager(
            max_size=100,
            default_ttl=7200,  # 2小时
            cleanup_interval=600
        )
        
        # LLM相关缓存
        self.llm_cache = AdvancedCacheManager(
            max_size=500,
            default_ttl=3600,  # 1小时
            cleanup_interval=300
        )
        
        # 数据库查询缓存
        self.db_cache = AdvancedCacheManager(
            max_size=300,
            default_ttl=1800,  # 30分钟
            cleanup_interval=300
        )
        
        # 文件处理缓存
        self.file_cache = AdvancedCacheManager(
            max_size=100,
            default_ttl=86400,  # 24小时
            cleanup_interval=3600
        )
    
    # 用户相关缓存方法
    def cache_user_info(self, user_id: str, user_info: Dict, ttl: Optional[int] = None) -> None:
        """缓存用户信息"""
        cache_key = f"user_info_{user_id}"
        self.user_cache.set(cache_key, user_info, ttl or 1800)
        logger.debug(f"缓存用户信息: {user_id}")
    
    def get_user_info(self, user_id: str) -> Optional[Dict]:
        """获取缓存的用户信息"""
        cache_key = f"user_info_{user_id}"
        return self.user_cache.get(cache_key)
    
    def cache_user_permissions(self, user_id: str, permissions: List[str], ttl: Optional[int] = None) -> None:
        """缓存用户权限"""
        cache_key = f"user_permissions_{user_id}"
        self.user_cache.set(cache_key, permissions, ttl or 3600)
        logger.debug(f"缓存用户权限: {user_id}")
    
    def get_user_permissions(self, user_id: str) -> Optional[List[str]]:
        """获取缓存的用户权限"""
        cache_key = f"user_permissions_{user_id}"
        return self.user_cache.get(cache_key)
    
    def invalidate_user_cache(self, user_id: str) -> None:
        """清除用户相关缓存"""
        patterns = [f"user_info_{user_id}", f"user_permissions_{user_id}"]
        for pattern in patterns:
            if self.user_cache.delete(pattern):
                logger.info(f"清除用户缓存: {pattern}")
    
    # 配置相关缓存方法
    def cache_config(self, config_key: str, config_value: Any, ttl: Optional[int] = None) -> None:
        """缓存配置值"""
        self.config_cache.set(config_key, config_value, ttl or 7200)
        logger.debug(f"缓存配置: {config_key}")
    
    def get_config(self, config_key: str) -> Any:
        """获取缓存的配置值"""
        return self.config_cache.get(config_key)
    
    def cache_llm_config(self, config_data: Dict, ttl: Optional[int] = None) -> None:
        """缓存LLM配置"""
        cache_key = "llm_config"
        self.config_cache.set(cache_key, config_data, ttl or 7200)
        logger.debug("缓存LLM配置")
    
    def get_llm_config(self) -> Optional[Dict]:
        """获取缓存的LLM配置"""
        return self.config_cache.get("llm_config")
    
    # LLM相关缓存方法
    def cache_llm_response(self, prompt: str, model: str, response: str, ttl: Optional[int] = None) -> None:
        """缓存LLM响应"""
        prompt_hash = hashlib.md5(f"{prompt}_{model}".encode()).hexdigest()
        cache_key = f"llm_response_{prompt_hash}"
        self.llm_cache.set(cache_key, response, ttl or 3600)
        logger.debug(f"缓存LLM响应: {prompt_hash}")
    
    def get_llm_response(self, prompt: str, model: str) -> Optional[str]:
        """获取缓存的LLM响应"""
        prompt_hash = hashlib.md5(f"{prompt}_{model}".encode()).hexdigest()
        cache_key = f"llm_response_{prompt_hash}"
        return self.llm_cache.get(cache_key)
    
    def cache_embedding(self, text: str, embedding: List[float], ttl: Optional[int] = None) -> None:
        """缓存文本嵌入向量"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        cache_key = f"embedding_{text_hash}"
        self.llm_cache.set(cache_key, embedding, ttl or 86400)
        logger.debug(f"缓存嵌入向量: {text_hash}")
    
    def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        cache_key = f"embedding_{text_hash}"
        return self.llm_cache.get(cache_key)
    
    # 数据库查询缓存方法
    def cache_db_query(self, query_key: str, result: Any, ttl: Optional[int] = None) -> None:
        """缓存数据库查询结果"""
        self.db_cache.set(query_key, result, ttl or 1800)
        logger.debug(f"缓存数据库查询: {query_key}")
    
    def get_db_query(self, query_key: str) -> Any:
        """获取缓存的数据库查询结果"""
        return self.db_cache.get(query_key)
    
    def cache_user_auth_ids(self, role_id: str, auth_ids: List[int], ttl: Optional[int] = None) -> None:
        """缓存用户权限ID"""
        cache_key = f"user_auth_ids_{role_id}"
        self.db_cache.set(cache_key, auth_ids, ttl or 3600)
        logger.debug(f"缓存用户权限ID: {role_id}")
    
    def get_user_auth_ids(self, role_id: str) -> Optional[List[int]]:
        """获取缓存的用户权限ID"""
        cache_key = f"user_auth_ids_{role_id}"
        return self.db_cache.get(cache_key)
    
    # 文件处理缓存方法
    def cache_file_content(self, file_path: str, content: str, ttl: Optional[int] = None) -> None:
        """缓存文件内容"""
        file_hash = hashlib.md5(file_path.encode()).hexdigest()
        cache_key = f"file_content_{file_hash}"
        self.file_cache.set(cache_key, content, ttl or 86400)
        logger.debug(f"缓存文件内容: {file_path}")
    
    def get_file_content(self, file_path: str) -> Optional[str]:
        """获取缓存的文件内容"""
        file_hash = hashlib.md5(file_path.encode()).hexdigest()
        cache_key = f"file_content_{file_hash}"
        return self.file_cache.get(cache_key)
    
    def cache_document_chunks(self, file_path: str, chunks: List[Dict], ttl: Optional[int] = None) -> None:
        """缓存文档分块结果"""
        file_hash = hashlib.md5(file_path.encode()).hexdigest()
        cache_key = f"doc_chunks_{file_hash}"
        self.file_cache.set(cache_key, chunks, ttl or 86400)
        logger.debug(f"缓存文档分块: {file_path}")
    
    def get_document_chunks(self, file_path: str) -> Optional[List[Dict]]:
        """获取缓存的文档分块"""
        file_hash = hashlib.md5(file_path.encode()).hexdigest()
        cache_key = f"doc_chunks_{file_hash}"
        return self.file_cache.get(cache_key)
    
    # 统计和管理方法
    def get_all_cache_stats(self) -> Dict[str, Any]:
        """获取所有缓存统计信息"""
        return {
            "user_cache": self.user_cache.get_stats(),
            "config_cache": self.config_cache.get_stats(),
            "llm_cache": self.llm_cache.get_stats(),
            "db_cache": self.db_cache.get_stats(),
            "file_cache": self.file_cache.get_stats(),
            "global_cache": global_cache.get_stats()
        }
    
    def clear_all_caches(self) -> None:
        """清空所有缓存"""
        caches = [
            ("user_cache", self.user_cache),
            ("config_cache", self.config_cache),
            ("llm_cache", self.llm_cache),
            ("db_cache", self.db_cache),
            ("file_cache", self.file_cache)
        ]
        
        for cache_name, cache_instance in caches:
            cache_instance.clear()
            logger.info(f"清空 {cache_name}")
        
        global_cache.clear()
        logger.info("清空全局缓存")
    
    def optimize_all_caches(self) -> Dict[str, int]:
        """优化所有缓存"""
        optimization_results = {}
        caches = [
            ("user_cache", self.user_cache),
            ("config_cache", self.config_cache),
            ("llm_cache", self.llm_cache),
            ("db_cache", self.db_cache),
            ("file_cache", self.file_cache)
        ]
        
        for cache_name, cache_instance in caches:
            initial_size = cache_instance.get_stats()["cache_size"]
            cache_instance._cleanup_expired()
            final_size = cache_instance.get_stats()["cache_size"]
            cleaned_items = initial_size - final_size
            optimization_results[cache_name] = cleaned_items
            
            if cleaned_items > 0:
                logger.info(f"{cache_name} 清理了 {cleaned_items} 个过期项")
        
        return optimization_results
    
    def get_cache_health_report(self) -> Dict[str, Any]:
        """获取缓存健康报告"""
        stats = self.get_all_cache_stats()
        health_report = {
            "overall_status": "healthy",
            "warnings": [],
            "recommendations": [],
            "cache_details": {}
        }
        
        for cache_name, cache_stats in stats.items():
            hit_rate = cache_stats["hit_rate"]
            usage_rate = cache_stats["cache_size"] / cache_stats["max_size"]
            
            cache_health = {
                "hit_rate": hit_rate,
                "usage_rate": usage_rate,
                "status": "healthy"
            }
            
            # 检查命中率
            if hit_rate < 0.5:
                cache_health["status"] = "warning"
                health_report["warnings"].append(f"{cache_name} 命中率较低 ({hit_rate:.2%})")
                health_report["recommendations"].append(f"考虑调整 {cache_name} 的缓存策略")
            
            # 检查使用率
            if usage_rate > 0.9:
                cache_health["status"] = "warning"
                health_report["warnings"].append(f"{cache_name} 使用率过高 ({usage_rate:.2%})")
                health_report["recommendations"].append(f"考虑增加 {cache_name} 的大小")
            
            health_report["cache_details"][cache_name] = cache_health
        
        if health_report["warnings"]:
            health_report["overall_status"] = "warning"
        
        return health_report


# 缓存装饰器工厂
def app_cached(cache_type: str = "global", ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """应用缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 选择缓存实例
            if cache_type == "global":
                cache_instance = global_cache
            else:
                cache_instance = getattr(app_cache, f"{cache_type}_cache", global_cache)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
            
            return cache_instance.get_or_create(
                cache_key,
                lambda: func(*args, **kwargs),
                ttl
            )
        
        return wrapper
    return decorator


# 全局应用缓存管理器实例
app_cache = AppCacheManager()


# 便捷函数
def cache_user_session(user_id: str, session_data: Dict, ttl: int = 1800):
    """缓存用户会话数据"""
    app_cache.cache_user_info(user_id, session_data, ttl)


def get_cached_user_session(user_id: str) -> Optional[Dict]:
    """获取缓存的用户会话数据"""
    return app_cache.get_user_info(user_id)


def cache_llm_call(prompt: str, model: str, response: str, ttl: int = 3600):
    """缓存LLM调用结果"""
    app_cache.cache_llm_response(prompt, model, response, ttl)


def get_cached_llm_call(prompt: str, model: str) -> Optional[str]:
    """获取缓存的LLM调用结果"""
    return app_cache.get_llm_response(prompt, model)


def clear_user_cache(user_id: str):
    """清除用户相关的所有缓存"""
    app_cache.invalidate_user_cache(user_id)


def get_cache_dashboard_data() -> Dict[str, Any]:
    """获取缓存仪表板数据"""
    return {
        "stats": app_cache.get_all_cache_stats(),
        "health": app_cache.get_cache_health_report(),
        "monitor": cache_monitor.get_current_metrics()
    }


if __name__ == '__main__':
    app_cache.get_all_cache_stats()