#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: menu_mapping.py 
@time: 2024/11/12 11:20
@Description: 
"""
import asyncio
from functools import wraps

import streamlit_antd_components as sac
import streamlit as st

from page.index import init_index
from page.chat import new_init_chat
from page.setting import init_setting
from page.statistics import init_statistics
from page.knowledge import init_knowledge
from page.user import init_user
from page.document import init_document_analysis
from page.test_case import main
from page.demand_analysis import demand_analysis_main
from page.cache_management import init_cache_management
from common.myLog import logger
from const.cache import cache

logger.name = __name__

def preserve_state(func):
    """将session_state存入自定义缓存"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        state_snapshot = {k: v for k, v in st.session_state.items()}
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            for k, v in state_snapshot.items():
                cache.put(k, v)
    return wrapper

def init_menu_mapping():
    """
    初始化菜单映射
    所有函数都是异步函数
    """
    return {
        '首页': init_index,
        '智能问答助手': new_init_chat,
        '文档分析助手': init_document_analysis,
        '需求分析助手': demand_analysis_main,
        '用例生成助手': main,
        '知识库': init_knowledge,
        '模型设置': init_setting,
        '数据中心': init_statistics,
        '系统管理': init_user,
        '缓存管理': init_cache_management
    }

@preserve_state
async def async_handle_menu(menu_selection: str) -> None:
    """异步处理菜单选择"""
    menu_mapping = init_menu_mapping()
    try:
        if menu_selection in menu_mapping:
            func = menu_mapping[menu_selection]
            await func()
        else:
            sac.result(label='页面不存在', description='404', status=404)
    except Exception as e:
        logger.error(f"处理菜单时出错: {e}")
        raise e

def handle_menu(menu_selection: str) -> None:
    """同步包装异步菜单处理函数"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(async_handle_menu(menu_selection))
    except Exception as e:
        logger.error(f"菜单处理错误: {e}")
        st.error("页面加载出错，请刷新重试")