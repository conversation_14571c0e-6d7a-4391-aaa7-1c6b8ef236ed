# 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# 基础配置
# ===========================================
ENV=dev
DEBUG=false
SECRET_KEY=your-secret-key-at-least-32-characters-long

# ===========================================
# 数据库配置
# ===========================================

# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=ym_rag

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=root
MILVUS_PASSWORD=your_milvus_password
MILVUS_DATABASE=YM

# ===========================================
# AI模型配置
# ===========================================

# LLM配置
LLM_BASE_URL=https://api.openai.com/v1
LLM_API_KEY=your_openai_api_key
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=4096

# 嵌入模型配置
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_MODEL_PATH=/path/to/local/model
EMBEDDING_API_BASE=https://api.openai.com/v1
EMBEDDING_API_KEY=your_embedding_api_key

# ===========================================
# 第三方服务配置
# ===========================================

# 钉钉配置
DINGTALK_CLIENT_ID=your_dingtalk_client_id
DINGTALK_CLIENT_SECRET=your_dingtalk_client_secret

# 搜索服务配置
GOOGLE_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
TAVILY_API_KEY=your_tavily_api_key

# Langfuse配置
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
LANGFUSE_HOST=https://cloud.langfuse.com

# ===========================================
# 安全配置
# ===========================================
ALLOWED_HOST=your-domain.com
CORS_ORIGIN=https://your-frontend.com

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO

# ===========================================
# 开发配置 (仅开发环境)
# ===========================================
# 开发环境特定配置
DEV_AUTO_RELOAD=true
DEV_DEBUG_TOOLBAR=true

# ===========================================
# 生产配置 (仅生产环境)
# ===========================================
# 生产环境特定配置
PROD_SSL_REQUIRED=true
PROD_PROFILING=true
