#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: models.py
@time: 2025/7/23 
@Description: 配置数据模型
"""
import os
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator, computed_field
from pydantic_settings import BaseSettings

from config.config_manager import config_manager


class DatabaseConfig(BaseModel):
    """数据库配置模型"""
    host: str = Field(..., description="数据库主机")
    port: int = Field(..., description="数据库端口")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    database: str = Field(..., description="数据库名")
    
    @field_validator("port")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("端口号必须在1-65535之间")
        return v
    
    @computed_field
    @property
    def url(self) -> str:
        """生成数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


class RedisConfig(BaseModel):
    """Redis配置模型"""
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    password: Optional[str] = Field(default=None, description="Redis密码")
    database: int = Field(default=0, description="Redis数据库编号")
    
    @field_validator("port")
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError("端口号必须在1-65535之间")
        return v
    
    @field_validator("database")
    def validate_database(cls, v):
        if not 0 <= v <= 15:
            raise ValueError("Redis数据库编号必须在0-15之间")
        return v
    
    @computed_field
    @property
    def url(self) -> str:
        """生成Redis连接URL"""
        auth_part = f":{self.password}@" if self.password else ""
        return f"redis://{auth_part}{self.host}:{self.port}/{self.database}"


class MilvusConfig(BaseModel):
    """Milvus配置模型"""
    host: str = Field(..., description="Milvus主机")
    port: int = Field(default=19530, description="Milvus端口")
    username: Optional[str] = Field(default=None, description="用户名")
    password: Optional[str] = Field(default=None, description="密码")
    database: str = Field(default="default", description="数据库名")
    
    @computed_field
    @property
    def uri(self) -> str:
        """生成Milvus连接URI"""
        return f"http://{self.host}:{self.port}"
    
    @computed_field
    @property
    def connection_params(self) -> Dict[str, Any]:
        """生成连接参数"""
        params = {
            "uri": self.uri,
            "db_name": self.database
        }
        if self.username:
            params["user"] = self.username
        if self.password:
            params["password"] = self.password
        return params


class LLMConfig(BaseModel):
    """LLM配置模型"""
    base_url: str = Field(..., description="API基础URL")
    api_key: str = Field(..., description="API密钥")
    model: str = Field(..., description="模型名称")
    temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="温度参数")
    max_tokens: Optional[int] = Field(default=None, ge=1, description="最大token数")
    timeout: int = Field(default=60, ge=1, description="超时时间(秒)")
    
    @field_validator("base_url")
    def validate_base_url(cls, v):
        if not v.startswith(("http://", "https://")):
            raise ValueError("base_url必须以http://或https://开头")
        return v.rstrip("/")


class EmbeddingConfig(BaseModel):
    """嵌入模型配置"""
    model_name: str = Field(..., description="模型名称")
    model_path: Optional[str] = Field(default=None, description="本地模型路径")
    api_base: Optional[str] = Field(default=None, description="API基础URL")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    batch_size: int = Field(default=32, ge=1, description="批处理大小")
    max_length: int = Field(default=512, ge=1, description="最大长度")


class SecurityConfig(BaseModel):
    """安全配置"""
    secret_key: str = Field(..., description="应用密钥")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_expire_hours: int = Field(default=24, ge=1, description="JWT过期时间(小时)")
    allowed_hosts: List[str] = Field(default_factory=list, description="允许的主机")
    cors_origins: List[str] = Field(default_factory=list, description="CORS源")
    
    @field_validator("secret_key")
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError("secret_key长度至少32个字符")
        return v


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
        description="日志格式"
    )
    file_path: Optional[str] = Field(default=None, description="日志文件路径")
    max_bytes: int = Field(default=10*1024*1024, description="日志文件最大大小")
    backup_count: int = Field(default=5, description="备份文件数量")
    
    @field_validator("level")
    def validate_level(cls, v):
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"日志级别必须是: {allowed_levels}")
        return v.upper()


class AppConfig(BaseSettings):
    """应用主配置"""
    # 环境配置
    env: str = Field(default="dev", description="运行环境")
    debug: bool = Field(default=False, description="调试模式")
    version: str = Field(default="2.0", description="应用版本")
    
    # 数据库配置
    mysql: DatabaseConfig = Field(..., description="MySQL配置")
    redis: RedisConfig = Field(..., description="Redis配置")
    milvus: MilvusConfig = Field(..., description="Milvus配置")
    
    # AI模型配置
    llm: LLMConfig = Field(..., description="LLM配置")
    embedding: EmbeddingConfig = Field(..., description="嵌入模型配置")
    
    # 安全配置
    security: SecurityConfig = Field(..., description="安全配置")
    
    # 日志配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig, description="日志配置")
    
    # 业务配置
    max_file_size: int = Field(default=100*1024*1024, description="最大文件大小")
    cache_ttl: int = Field(default=3600, description="缓存TTL(秒)")
    
    @field_validator("env")
    def validate_env(cls, v):
        allowed_envs = ["dev", "test", "prod"]
        if v not in allowed_envs:
            raise ValueError(f"环境必须是: {allowed_envs}")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        @classmethod
        def customise_sources(cls, init_settings, env_settings, file_secret_settings):
            """自定义配置源优先级"""
            return (
                init_settings,  # 初始化参数 (最高优先级)
                env_settings,   # 环境变量
                file_secret_settings,  # 文件配置 (最低优先级)
            )


def load_config_from_env() -> AppConfig:
    """从环境变量加载配置"""
    try:
        # 数据库配置
        mysql_config = DatabaseConfig(
            host=config_manager.get_env_config("MYSQL_HOST", "localhost"),
            port=config_manager.get_env_config("MYSQL_PORT", 3306, int),
            username=config_manager.get_env_config("MYSQL_USER", required=True),
            password=config_manager.get_env_config("MYSQL_PASSWORD", required=True),
            database=config_manager.get_env_config("MYSQL_DATABASE", required=True)
        )
        
        # Redis配置
        redis_config = RedisConfig(
            host=config_manager.get_env_config("REDIS_HOST", "localhost"),
            port=config_manager.get_env_config("REDIS_PORT", 6379, int),
            password=config_manager.get_env_config("REDIS_PASSWORD"),
            database=config_manager.get_env_config("REDIS_DB", 0, int)
        )
        
        # Milvus配置
        milvus_config = MilvusConfig(
            host=config_manager.get_env_config("MILVUS_HOST", required=True),
            port=config_manager.get_env_config("MILVUS_PORT", 19530, int),
            username=config_manager.get_env_config("MILVUS_USER"),
            password=config_manager.get_env_config("MILVUS_PASSWORD"),
            database=config_manager.get_env_config("MILVUS_DATABASE", "default")
        )
        
        # LLM配置
        llm_config = LLMConfig(
            base_url=config_manager.get_env_config("LLM_BASE_URL", required=True),
            api_key=config_manager.get_env_config("LLM_API_KEY", required=True),
            model=config_manager.get_env_config("LLM_MODEL", required=True),
            temperature=config_manager.get_env_config("LLM_TEMPERATURE", 0.1, float),
            max_tokens=config_manager.get_env_config("LLM_MAX_TOKENS", None, int)
        )
        
        # 嵌入模型配置
        embedding_config = EmbeddingConfig(
            model_name=config_manager.get_env_config("EMBEDDING_MODEL", required=True),
            model_path=config_manager.get_env_config("EMBEDDING_MODEL_PATH"),
            api_base=config_manager.get_env_config("EMBEDDING_API_BASE"),
            api_key=config_manager.get_env_config("EMBEDDING_API_KEY")
        )
        
        # 安全配置
        security_config = SecurityConfig(
            secret_key=config_manager.get_env_config("SECRET_KEY", required=True),
            allowed_hosts=config_manager.get_env_config("ALLOWED_HOSTS", [], list),
            cors_origins=config_manager.get_env_config("CORS_ORIGINS", [], list)
        )
        
        # 创建主配置
        app_config = AppConfig(
            env=config_manager.get_env_config("ENV", "dev"),
            debug=config_manager.get_env_config("DEBUG", False, bool),
            mysql=mysql_config,
            redis=redis_config,
            milvus=milvus_config,
            llm=llm_config,
            embedding=embedding_config,
            security=security_config
        )
        
        return app_config
        
    except Exception as e:
        logger.error(f"配置加载失败: {str(e)}")
        raise
