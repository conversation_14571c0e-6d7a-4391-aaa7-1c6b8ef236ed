#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: statistics.py
@time: 2024/9/29 15:07
@Description:
"""

import pandas as pd
import streamlit as st
from pandas import DataFrame

from const.constants import Q_MAP
from const.style import statistical_title_css
from config.config_center import settings
from common.myLog import logger

logger.name = __name__

# 常量定义
CHART_HEIGHT = 666
CACHE_TTL = 3600
DEFAULT_TOP_N = 10
DATE_RANGE = 30

class DataVisualization:
    """数据可视化处理类"""
    
    def __init__(self):
        self.color = settings.ui.global_color
        self.header_divider = settings.ui.header_divider
    
    @staticmethod
    @st.cache_data(ttl=CACHE_TTL)
    def get_top_n() -> int:
        """获取要展示的TOP N配置值"""
        try:
            return st.session_state.curd.getConfigValue("q_top")
        except Exception as e:
            logger.error(f"获取TOP N配置失败: {e}")
            return DEFAULT_TOP_N

    @staticmethod
    def create_chart_title(title: str) -> None:
        """创建图表标题"""
        st.markdown(
            f'<p class="statistical-title">{title}</p>',
            unsafe_allow_html=True
        )

    @staticmethod
    def process_top_queries(top_n: int) -> DataFrame:
        """处理查询排名数据"""
        try:
            data = st.session_state.curd.query_top(top=top_n)
            df = pd.DataFrame(data, columns=['问题：', '提问次数：'])
            return df.set_index('问题：')
        except Exception as e:
            logger.error(f"处理查询排名数据失败: {e}")
            raise

    @staticmethod
    def process_message_count() -> DataFrame:
        """处理消息统计数据"""
        try:
            data = st.session_state.curd.message_count
            df = pd.DataFrame(data, columns=['date', 'count'])
            return df.set_index('date')
        except Exception as e:
            logger.error(f"处理消息统计数据失败: {e}")
            raise

    @staticmethod
    def process_type_statistics() -> DataFrame:
        """处理类型统计数据"""
        try:
            data = st.session_state.curd.message_type_count
            df = pd.DataFrame(data, columns=['date', 'type', 'count'])
            df['type_description'] = df['type'].map(dict(Q_MAP))
            return df.pivot(
                index='date',
                columns='type_description',
                values='count'
            ).fillna(0)
        except Exception as e:
            logger.error(f"处理类型统计数据失败: {e}")
            raise

async def init_statistics() -> None:
    """初始化数据统计页面"""
    try:
        viz = DataVisualization()
        st.header("数据统计", divider=viz.header_divider)
        st.markdown(statistical_title_css, unsafe_allow_html=True)

        # 创建标签页
        q_top, q_count, q_type_count = st.tabs(["查询排名", "提问统计", "类型统计"])

        # 查询排名展示
        with q_top:
            top_n = viz.get_top_n()
            viz.create_chart_title(f"近30日 Query Top {top_n} 🔥")
            df_top = viz.process_top_queries(top_n)
            st.bar_chart(df_top, color=viz.color, height=CHART_HEIGHT)

        # 提问统计展示
        with q_count:
            viz.create_chart_title("近30日提问统计")
            df_count = viz.process_message_count()
            st.line_chart(df_count, color=viz.color, height=CHART_HEIGHT)

        # 类型统计展示
        with q_type_count:
            viz.create_chart_title("近30日问答类型统计")
            df_type = viz.process_type_statistics()
            st.line_chart(df_type, height=CHART_HEIGHT)

    except Exception as e:
        logger.error(f"初始化数据中心页面失败: {e}")
        st.error("加载数据统计信息失败，请稍后重试")
