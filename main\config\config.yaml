# 应用配置文件
# 注意: 敏感信息应通过环境变量设置，不要直接写在配置文件中

app:
  name: "约苗智能问答助手"
  version: "2.0"
  debug: false
  environment: "${ENV:-dev}"

# 数据库配置 - 通过环境变量设置
database:
  mysql:
    host: "${MYSQL_HOST:-localhost}"
    port: "${MYSQL_PORT:-3306}"
    username: "${MYSQL_USER}"
    password: "${MYSQL_PASSWORD}"
    database: "${MYSQL_DATABASE}"
    pool_size: 10
    max_overflow: 20
    pool_timeout: 30
    pool_recycle: 3600

  redis:
    host: "${REDIS_HOST:-localhost}"
    port: "${REDIS_PORT:-6379}"
    password: "${REDIS_PASSWORD}"
    database: "${REDIS_DB:-0}"
    max_connections: 50
    socket_timeout: 5
    socket_connect_timeout: 5

  milvus:
    host: "${MILVUS_HOST}"
    port: "${MILVUS_PORT:-19530}"
    username: "${MILVUS_USER}"
    password: "${MILVUS_PASSWORD}"
    database: "${MILVUS_DATABASE:-default}"
    timeout: 30

# AI模型配置
ai:
  llm:
    base_url: "${LLM_BASE_URL}"
    api_key: "${LLM_API_KEY}"
    model: "${LLM_MODEL}"
    temperature: 0.1
    max_tokens: 4096
    timeout: 60
    retry_attempts: 3
    retry_delay: 1

  embedding:
    model_name: "${EMBEDDING_MODEL}"
    model_path: "${EMBEDDING_MODEL_PATH}"
    api_base: "${EMBEDDING_API_BASE}"
    api_key: "${EMBEDDING_API_KEY}"
    batch_size: 32
    max_length: 512
    device: "cpu"

# 缓存配置
cache:
  default_ttl: 3600  # 1小时
  max_size: 1000
  cleanup_interval: 300  # 5分钟
  
  # 不同类型缓存的TTL设置
  ttl_settings:
    router_chain: 1800    # 30分钟
    rag_chain: 3600       # 1小时
    common_chain: 1800    # 30分钟
    retriever: 7200       # 2小时

# 检索配置
retrieval:
  strategies:
    - "Base"
    - "MultiQuery" 
    - "Compression"
    - "Rerank"
  default_strategy: "Rerank"
  top_k: 5
  similarity_threshold: 0.7

# 文件处理配置
file_processing:
  max_file_size: ********0  # 100MB
  allowed_extensions:
    - "txt"
    - "pdf"
    - "docx"
    - "md"
    - "csv"
  chunk_size: 500
  chunk_overlap: 200

# 安全配置
security:
  secret_key: "${SECRET_KEY}"
  jwt_algorithm: "HS256"
  jwt_expire_hours: 24
  allowed_hosts:
    - "localhost"
    - "127.0.0.1"
    - "${ALLOWED_HOST}"
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8501"
    - "${CORS_ORIGIN}"

# 日志配置
logging:
  level: "${LOG_LEVEL:-INFO}"
  format: "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
  handlers:
    console:
      enabled: true
      level: "INFO"
    file:
      enabled: true
      level: "DEBUG"
      path: "logs/app.log"
      max_bytes: ********  # 10MB
      backup_count: 5
    error_file:
      enabled: true
      level: "ERROR"
      path: "logs/error.log"
      max_bytes: ********  # 10MB
      backup_count: 5

# 监控配置
monitoring:
  metrics:
    enabled: true
    port: 9090
  health_check:
    enabled: true
    endpoint: "/health"
    timeout: 30
  performance:
    slow_query_threshold: 1.0  # 秒
    memory_threshold: 0.8      # 80%

# 业务配置
business:
  memory_key: "message_store"
  session_timeout: 7200  # 2小时
  max_history_length: 100
  
  # 路由规则
  routing_rules:
    technical: ["API", "SDK", "Docker", "数据库", "部署", "日志"]
    business: ["预约", "疫苗", "小程序", "公众号", "订单", "支付"]
    admin: ["请假", "报销", "离职", "绩效", "合同", "采购"]

  # 默认问题
  default_questions:
    - "接种卡相关问题有哪些？"
    - "公众号无法访问如何处理？"
    - "关于医生积分发放问题解答"
    - "约苗管家及门诊相关问题？"

# 第三方服务配置
external_services:
  dingtalk:
    client_id: "${DINGTALK_CLIENT_ID}"
    client_secret: "${DINGTALK_CLIENT_SECRET}"
    api_url: "https://api.dingtalk.com"
    oapi_url: "https://oapi.dingtalk.com/"
    
  search:
    google:
      api_key: "${GOOGLE_API_KEY}"
      search_engine_id: "${GOOGLE_SEARCH_ENGINE_ID}"
    tavily:
      api_key: "${TAVILY_API_KEY}"
      max_results: 5
      
  langfuse:
    public_key: "${LANGFUSE_PUBLIC_KEY}"
    secret_key: "${LANGFUSE_SECRET_KEY}"
    host: "${LANGFUSE_HOST}"

# 开发配置
development:
  auto_reload: true
  debug_toolbar: true
  profiling: false
  mock_external_services: false

# 生产配置
production:
  auto_reload: false
  debug_toolbar: false
  profiling: true
  ssl_required: true
  
# 测试配置
testing:
  database_url: "sqlite:///test.db"
  cache_backend: "memory"
  mock_external_services: true
