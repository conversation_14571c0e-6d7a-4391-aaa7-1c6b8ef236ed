#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: myEmb.py 
@time: 2024/9/18 15:20
@Description: 
"""

import os
from functools import lru_cache
from dotenv import load_dotenv
from typing import Dict, List, Any

from langchain.embeddings.base import Embeddings
from langchain.pydantic_v1 import BaseModel, root_validator
from tenacity import retry, stop_after_attempt, wait_exponential

from const.cache import cache
from common.myLog import logger
from config.config_center import settings

load_dotenv()

base_url = os.environ.get("EMB_BASE_URL")
api_key = os.environ.get("API_KEY")
embedding_name = os.environ.get("EMBEDDING_MODEL")

logger.name = __name__


class MyEmbeddings(BaseModel, Embeddings):

    client: Any
    emb_config: Dict = {}

    @root_validator(allow_reuse=True)
    def load_config(cls, values: Dict) -> Dict:
        try:
            values["emb_config"] = cache.get_all()['emb']
            logger.info(f"Load Cache Embedding Config: {values['emb_config']}")
        except:
            values["emb_config"] = {
                "base_url": base_url,
                "api_key": api_key,
                "model": embedding_name
            }
            logger.info(f"Load Env Embedding Config: {values['emb_config']}")
        return values

    @root_validator(allow_reuse=True)
    def validate_environment(cls, values: Dict) -> Dict:
        from openai import OpenAI
        values["client"] = OpenAI(
            base_url=values["emb_config"].get("base_url", base_url),
            api_key=values["emb_config"].get("api_key", api_key),
            max_retries=5
        )
        return values

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def embed_query(self, text: str) -> List[float]:
        try:
            embeddings = self.client.embeddings.create(
                model=self.emb_config.get("model", embedding_name),
                input=text
            )
            return embeddings.data[0].embedding
        except Exception as e:
            logger.error(f"Embedding 生成失败: {str(e)}")
            raise

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        results = []
        for text in texts:
            try:
                embedding = self.embed_query(text)
                results.append(embedding)
            except Exception as e:
                logger.error(f"文档 Embedding 生成失败: {str(e)}")
                raise
        return results

def init_embedding():
    return MyEmbeddings()
