#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: app.py
@time: 2024/9/29 15:07
@Description:
"""
import time
from typing import Dict, List
from dataclasses import dataclass

import streamlit as st
import streamlit_antd_components as sac

from common.myLog import logger
from config.config_center import settings
from common.initialize import init_data, Login, init_llm, init_curd
from common.utils import Year, get_time_period, measure_time, error_handler, with_loading, safe_markdown
from const.enums import SystemMenuEnum
from const.style import centered_css
from const.menu_mapping import handle_menu



@dataclass
class AppConfig:
    """应用配置类"""
    page_config = settings.ui.get_page_config
    logo_config = settings.ui.get_logo_config
    login_url = settings.dingtalk.DingLoginUrl
    version = settings.version


class AuthManager:
    """用户认证管理类"""
    @staticmethod
    def check_auth(user_info: Dict) -> bool:
        return any([
            user_info.get("is_admin"),
            user_info.get("is_super")
        ])

    @staticmethod
    def get_user_menu(auth_ids: List[int], is_auth: bool) -> List:
        menu_system = SystemMenuEnum(auth_ids, is_auth)
        return [menu_system().get(i) for i in auth_ids if i in menu_system()]

def check_session_expired():
    """检查会话是否过期"""
    if 'last_activity' in st.session_state:
        if time.time() - st.session_state.last_activity > 7200:
            st.session_state.clear()
            st.info("会话已过期，请重新登录")
            st.stop()
    st.session_state.last_activity = time.time()


@measure_time
@error_handler
def main():
    """主程序"""
    # 检查会话状态
    check_session_expired()
    
    # 初始化配置
    app_config = AppConfig()
    st.set_page_config(**app_config.page_config)
    st.logo(**app_config.logo_config)
    
    # 初始化数据库连接
    st.session_state.curd = init_curd()

    # 登录验证
    Login(st.query_params.to_dict())
    
    if "userInfo" not in st.session_state:
        safe_markdown(centered_css, allow_html=True)
        URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)
        safe_markdown(f"""
        <div class="body-container">
            <div>
                <h1>{settings.ui.rag_header}</h1>
                <a href="{URL}" target="_self" class="bt-link">开始使用</a>
            </div>   
        </div>
        """, allow_html=True)
        safe_markdown(f"""
        <div class="ym-container">
            <span>© {Year} <a href="{settings.business.URL}" style="{settings.ui.ym_link_style}">约苗</a></span>
        </div>
        """, allow_html=True)
        st.stop()

    # 初始化LLM
    if "llm" not in st.session_state:
        with_loading("正在初始化模型...")(init_llm)()
        logger.info(f"LLM初始化完成:{st.session_state}")

    # 初始化数据
    init_data()

    # 侧边栏
    with st.sidebar:
        safe_markdown(f"""
            # {get_time_period()}，{st.session_state.userInfo.get("dt_name", "")}
            - `LLM： {st.session_state.llm['model']}`
            - `EMBEDDING： {st.session_state.emb['model']}`
            - `RETRIEVAL： {st.session_state.searchType}`
            - `TEMPERATURE：{st.session_state.temperature}`
            - `VERSION： {app_config.version}`
            """,
            allow_html=True
        )
        
        # 权限管理
        auth_manager = AuthManager()
        AUTH = auth_manager.check_auth(st.session_state.userInfo)
        auth_ids = st.session_state.curd.get_user_auth(
            role_id=st.session_state.userInfo.get("roles_id")
        )

        # 菜单生成
        Index = 2 if AUTH else 2
        SystemMenuItems = auth_manager.get_user_menu(auth_ids, AUTH)
        sac.menu(
            SystemMenuItems,
            open_index=1,
            key='menu',
            color='blue',
            index=Index
        )

    handle_menu(st.session_state.menu)


if __name__ == "__main__":
    main()
