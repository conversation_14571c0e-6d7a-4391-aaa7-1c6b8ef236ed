#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: demand_analysis.py
@time: 2025/4/10 16:17
@Description: 需求分析助手
"""
import json
from typing import Dict, AsyncIterator, List, Any

import streamlit as st

from config.config_center import settings
from common.utils import get_system_avatar, ExtractTestCase, remove_empty_lines
from common.base_ui import BaseUI
from common.base_generator import BaseGenerator
from common.error_handler import ErrorHandler
from common.myLog import logger

logger.name = __name__

class DemandAnalysisGenerator(BaseGenerator):
    """需求分析助手生成器"""

    def __init__(self):
        super().__init__(prompt_name="demand_analysis")

class DemandAnalysisUI(BaseUI):
    """需求分析页面UI组件"""

    def __init__(self):
        super().__init__(
            session_key="test_point_messages",
            welcome_message="您好！我是 :red[需求分析]智能助手。您只需提供需求文档，我便能精准为您提取业务测试点哦。"
        )
        self.generator = DemandAnalysisGenerator()

    async def render_header(self):
        """渲染页面头部"""
        await super().render_header("需求")

    async def process_prompt(self, prompt: str):
        """处理需求分析"""
        with st.chat_message("assistant", avatar=get_system_avatar()):
            with st.spinner("正在全力分析需求，AI助手为您生成精准测试点，请耐心等待..."):
                await self._generate_test_point(prompt)

    @ErrorHandler.handle_api_error
    async def _generate_test_point(self, prompt: str):
        """生成测试点"""
        question = {"question": prompt}

        with st.expander("业务测试点分析 - 点击展开查看测试点详细分析内容", expanded=False, icon="🧪"):
            response_container = st.empty()
            full_response = ""

            async for response in self.generator.generate_content(
                    question,
                    st.session_state.userInfo["dt_userid"]
            ):
                if response:
                    full_response += response
                    with response_container:
                        st.markdown(full_response)

        json_data = ExtractTestCase(full_response)
        self._display_test_cases(json_data)
        self.add_assistant_message(full_response)

    @staticmethod
    def _display_test_cases(json_data: List[Dict[str, Any]]):
        """表格显示测试点"""
        if not json_data:
            st.warning("未能成功提取测试点数据，请检查输入或重试")
            return
            
        st.dataframe(json_data, use_container_width=True)

async def demand_analysis_main():
    """主函数"""
    ui = DemandAnalysisUI()
    await ui.render_header()
    await ui.render_chat_history()

    if prompt := st.chat_input("请输入你的问题，按Enter键发送。"):
        await ui.handle_user_input(prompt)
        await ui.process_prompt(prompt)