#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: base.py
@time: 2024/4/11 13:10
@Description:
"""
from sqlalchemy import INT, DATETIME, Column, func, String, BOOLEAN, Index, Text

from const.constants import DATA_CODING
from model import Base, engine


class YmRagBaseModel(Base):
    id = Column(INT, primary_key=True, comment="主键id")
    create_time = Column(DATETIME, nullable=False, server_default=func.current_timestamp(), comment="创建时间")
    modify_time = Column(DATETIME, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp(), comment="更新时间")
    __abstract__ = True


class TbUser(YmRagBaseModel):
    __tablename__ = 'tb_user'

    __table_args__ = (
        Index('idx_dt_userid', 'dt_userid'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '用户信息表'}
    )

    dt_userid = Column(String(50), nullable=False, comment="钉钉userid")
    dt_name = Column(String(30), nullable=False, comment="钉钉name")
    dt_title = Column(String(50), nullable=False, comment="钉钉title")
    dt_mobile = Column(String(20), nullable=False, comment="钉钉mobile")
    dt_unionid = Column(String(100), nullable=False, comment="钉钉unionid")
    roles_id = Column(String(128), nullable=False, default='', comment="角色id，多个以逗号分割")
    is_super = Column(BOOLEAN, nullable=False, default=0, comment="超级管理员，1-是 0-否")
    is_admin = Column(BOOLEAN, nullable=False, default=0, comment="管理员，1-是 0-否")
    is_active = Column(BOOLEAN, nullable=False, default=1, comment="在职状态，1-是 0-否")


class TbMessage(YmRagBaseModel):
    __tablename__ = 'tb_message'

    __table_args__ = (
        Index('idx_role', 'role'),
        Index('idx_type', 'type'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '消息表'}
    )

    dt_userid = Column(String(50), nullable=False, server_default='', comment="钉钉userid")
    dt_name = Column(String(30), nullable=False, server_default='', comment="钉钉name")
    role = Column(String(16), nullable=False, server_default='', comment="角色类型 USER(用户)、SYSTEM(系统)、ASSISTANT(助手)")
    type = Column(String(16), nullable=False, server_default='', comment="问答类型 TQ(技术)、PQ(产品)、CSQ(客服)、AQ(规章制度)")
    content = Column(Text, nullable=False, comment="消息内容")


class TbRole(YmRagBaseModel):
    __tablename__ = 'tb_roles'

    __table_args__ = (
        Index('idx_role_name', 'role_name'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '角色表'}
    )

    role_name = Column(String(30), nullable=False, comment="角色名称")
    auth_id = Column(String(128), nullable=False, default='', comment="权限id，多个以逗号分割")
    remark = Column(String(256), nullable=True, comment="备注")
    yn = Column(BOOLEAN, default=0, nullable=False, comment="0: 未删除 1: 已删除")


class TbAuth(YmRagBaseModel):
    __tablename__ = 'tb_auths'

    __table_args__ = (
        Index('idx_auth_name', 'auth_name'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '权限表'}
    )

    auth_name = Column(String(30), nullable=False, comment="权限名称")
    remark = Column(String(256), nullable=True, comment="备注")
    yn = Column(BOOLEAN, default=0, nullable=False, comment="0: 未删除 1: 已删除")


class TbConfig(YmRagBaseModel):
    __tablename__ = 'tb_config'

    __table_args__ = (
        Index('idx_config_name', 'config_name'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '配置表'}
    )

    config_name = Column(String(30), nullable=False, comment="配置名称")
    config_value = Column(String(256), nullable=False, comment="配置内容")
    create_name = Column(String(30), nullable=False, comment="创建人")
    remark = Column(String(256), nullable=True, comment="备注")
    yn = Column(BOOLEAN, default=0, nullable=False, comment="0: 未删除 1: 已删除")


class TbKnowledge(YmRagBaseModel):
    __tablename__ = 'tb_knowledge'

    __table_args__ = (
        Index('idx_document_name', 'document_name'),
        Index('idx_section', 'section'),
        Index('idx_create_time', 'create_time'),
        DATA_CODING | {'comment': '知识库表'}
    )

    document_name = Column(String(30), nullable=False, comment="文件名称")
    section = Column(String(30), nullable=False, comment="所属部门")
    group = Column(String(30), nullable=False, comment="所属组")
    relevance_ai = Column(String(30), nullable=True, comment="关联AI")
    create_name = Column(String(30), nullable=False, comment="创建人")
    yn = Column(BOOLEAN, default=0, nullable=False, comment="0: 未删除 1: 已删除")


if __name__ == '__main__':
    Base.metadata.create_all(engine)