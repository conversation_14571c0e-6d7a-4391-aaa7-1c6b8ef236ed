#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: optimized_app.py
@time: 2025/7/24
@Description: 优化后的应用启动文件
"""
import time
from typing import Dict, List
from dataclasses import dataclass

import streamlit as st
import streamlit_antd_components as sac

from common.myLog import logger
from config.config_center import settings
from common.initialize import Login, init_data
from common.utils import Year, get_time_period, measure_time, error_handler, safe_markdown
from const.enums import SystemMenuEnum
from const.style import centered_css
from const.menu_mapping import handle_menu

# 导入优化模块
from optimization.async_initializer import (
    async_initializer, lazy_initializer, create_optimized_init_tasks
)
from optimization.db_optimizer import init_optimized_database
from optimization.cache_optimizer import get_cache_manager, warmup_cache, optimized_cache

# 导入高级缓存管理器
from main.common.cache_manager import AdvancedCacheManager, cached_method, global_cache
from main.common.cache_monitor import cache_manager as cache_monitor


@dataclass
class AppConfig:
    """应用配置类"""
    page_config = settings.ui.get_page_config
    logo_config = settings.ui.get_logo_config
    login_url = settings.dingtalk.DingLoginUrl
    version = settings.version


class AuthManager:
    """用户认证管理类 - 使用高级缓存管理器"""

    def __init__(self):
        # 创建专用的认证缓存
        self.auth_cache = AdvancedCacheManager(
            max_size=100,
            default_ttl=1800,  # 30分钟
            cleanup_interval=300
        )

    def check_auth(self, user_info: Dict) -> bool:
        """检查用户权限 - 带缓存"""
        user_id = user_info.get("user_id", "unknown")
        cache_key = f"auth_check_{user_id}_{hash(str(user_info))}"

        def compute_auth():
            logger.info(f"计算用户权限: {user_id}")
            return any([
                user_info.get("is_admin"),
                user_info.get("is_super")
            ])

        return self.auth_cache.get_or_create(
            cache_key,
            compute_auth,
            ttl=1800  # 30分钟
        )

    def get_user_menu(self, auth_ids: List[int], is_auth: bool) -> List:
        """获取用户菜单 - 带缓存"""
        cache_key = f"user_menu_{hash(str(auth_ids))}_{is_auth}"

        def compute_menu():
            logger.info(f"计算用户菜单: {auth_ids}")
            menu_system = SystemMenuEnum(auth_ids, is_auth)
            return [menu_system().get(i) for i in auth_ids if i in menu_system()]

        return self.auth_cache.get_or_create(
            cache_key,
            compute_menu,
            ttl=3600  # 1小时
        )

    def invalidate_user_cache(self, user_id: str):
        """清除用户相关缓存"""
        # 这里可以实现按模式清理缓存
        cache_info = self.auth_cache.get_cache_info()
        keys_to_delete = [
            key for key in cache_info.keys()
            if f"_{user_id}_" in key
        ]

        for key in keys_to_delete:
            self.auth_cache.delete(key)
            logger.info(f"清除用户缓存: {key}")


class OptimizedAppInitializer:
    """优化的应用初始化器 - 集成高级缓存管理"""

    def __init__(self):
        # 使用高级缓存管理器
        self.app_cache = AdvancedCacheManager(
            max_size=500,
            default_ttl=3600,  # 1小时
            cleanup_interval=300
        )
        self.initialization_start_time = time.time()
        self.auth_manager = AuthManager()
        
    def check_session_expired(self):
        """检查会话是否过期"""
        if 'last_activity' in st.session_state:
            if time.time() - st.session_state.last_activity > 7200:
                st.session_state.clear()
                st.info("会话已过期，请重新登录")
                st.stop()
        st.session_state.last_activity = time.time()
    
    def initialize_basic_config(self):
        """初始化基础配置"""
        app_config = AppConfig()
        st.set_page_config(**app_config.page_config)
        st.logo(**app_config.logo_config)
        return app_config
    
    def initialize_database(self):
        """初始化数据库连接"""
        if "curd" not in st.session_state:
            st.session_state.curd = init_optimized_database()
            logger.info("优化数据库连接初始化完成")
    
    def handle_login_flow(self, app_config: AppConfig):
        """处理登录流程 - 使用高级缓存"""
        query_params = st.query_params.to_dict()
        cached_login_key = f"login_{hash(str(query_params))}"

        # 检查登录缓存
        def perform_login():
            logger.info("执行登录验证")
            Login(query_params)
            return "userInfo" in st.session_state

        # 使用缓存的登录验证
        login_success = self.app_cache.get_or_create(
            cached_login_key,
            perform_login,
            ttl=1800  # 30分钟
        )

        if not login_success or "userInfo" not in st.session_state:
            self._render_login_page(app_config)
            st.stop()
    
    def _render_login_page(self, app_config: AppConfig):
        """渲染登录页面"""
        safe_markdown(centered_css, allow_html=True)
        URL = app_config.login_url.format(settings.api.BASE_URL, settings.dingtalk.clientId)
        safe_markdown(f"""
        <div class="body-container">
            <div>
                <h1>{settings.ui.rag_header}</h1>
                <a href="{URL}" target="_self" class="bt-link">开始使用</a>
            </div>   
        </div>
        """, allow_html=True)
        safe_markdown(f"""
        <div class="ym-container">
            <span>© {Year} <a href="{settings.business.URL}" style="{settings.ui.ym_link_style}">约苗</a></span>
        </div>
        """, allow_html=True)
    
    def initialize_llm_lazy(self):
        """懒加载LLM初始化"""
        if "llm" not in st.session_state:
            # 注册懒加载初始化器
            lazy_initializer.register("llm", self._init_llm_with_cache)
            
            # 显示加载状态但不阻塞
            with st.spinner("正在初始化模型..."):
                # 异步初始化LLM
                if not lazy_initializer.is_initialized("llm"):
                    # 在后台线程中初始化
                    import threading
                    def background_init():
                        try:
                            result = lazy_initializer.get("llm")
                            st.session_state.llm = result["llm"]
                            st.session_state.llm_comm = result["llm_comm"]
                            st.session_state.emb = result["emb"]
                            st.session_state.search_type = result["search_type"]
                            st.session_state.base_k = result["base_k"]
                            st.session_state.top_k = result["top_k"]
                            logger.info("LLM后台初始化完成")
                        except Exception as e:
                            logger.error(f"LLM后台初始化失败: {str(e)}")
                    
                    thread = threading.Thread(target=background_init, daemon=True)
                    thread.start()
                    
                    # 设置默认值以避免阻塞
                    st.session_state.llm = {"model": "正在初始化..."}
                    st.session_state.llm_comm = {"model": "正在初始化..."}
                    st.session_state.emb = {"model": "正在初始化..."}
                    st.session_state.search_type = "正在初始化..."
                    st.session_state.base_k = 0
                    st.session_state.top_k = 0
    
    def _init_llm_with_cache(self):
        """带缓存的LLM初始化 - 使用高级缓存"""
        cache_key = "llm_config_init"

        def load_llm_config():
            logger.info("从数据库加载LLM配置")

            # 获取配置值
            configs = st.session_state.curd.getConfigValues(
                ["llm", "llm_comm", "emb", "search_type", "base_k", "top_k"]
            )

            import json
            return {
                "llm": json.loads(configs["llm"]),
                "llm_comm": json.loads(configs["llm_comm"]),
                "emb": json.loads(configs["emb"]),
                "search_type": configs["search_type"],
                "base_k": configs["base_k"],
                "top_k": configs["top_k"]
            }

        return self.app_cache.get_or_create(
            cache_key,
            load_llm_config,
            ttl=7200  # 2小时
        )
    
    def initialize_session_data(self):
        """初始化会话数据"""
        if "session_data_initialized" not in st.session_state:
            init_data()
            st.session_state.session_data_initialized = True
    
    def render_sidebar(self, app_config: AppConfig):
        """渲染侧边栏"""
        with st.sidebar:
            # 获取LLM状态
            llm_model = st.session_state.get("llm", {}).get("model", "未初始化")
            emb_model = st.session_state.get("emb", {}).get("model", "未初始化")
            search_type = st.session_state.get("searchType", "未设置")
            temperature = st.session_state.get("temperature", 0.1)
            
            safe_markdown(f"""
                # {get_time_period()}，{st.session_state.userInfo.get("dt_name", "")}
                - `LLM： {llm_model}`
                - `EMBEDDING： {emb_model}`
                - `RETRIEVAL： {search_type}`
                - `TEMPERATURE：{temperature}`
                - `VERSION： {app_config.version}`
                """,
                allow_html=True
            )
            
            # 权限管理 - 使用缓存的认证管理器
            AUTH = self.auth_manager.check_auth(st.session_state.userInfo)

            # 获取用户权限ID（带缓存）
            user_role_id = st.session_state.userInfo.get("roles_id")
            auth_ids_cache_key = f"user_auth_ids_{user_role_id}"

            def get_auth_ids():
                return st.session_state.curd.get_user_auth(role_id=user_role_id)

            auth_ids = self.app_cache.get_or_create(
                auth_ids_cache_key,
                get_auth_ids,
                ttl=3600  # 1小时
            )

            # 菜单生成
            Index = 2 if AUTH else 2
            SystemMenuItems = self.auth_manager.get_user_menu(auth_ids, AUTH)
            sac.menu(
                SystemMenuItems,
                open_index=1,
                key='menu',
                color='blue',
                index=Index
            )
    
    def get_initialization_stats(self) -> Dict:
        """获取初始化统计信息 - 包含高级缓存统计"""
        total_time = time.time() - self.initialization_start_time

        # 收集所有缓存统计
        cache_stats = {
            "app_cache": self.app_cache.get_stats(),
            "auth_cache": self.auth_manager.auth_cache.get_stats(),
            "global_cache": global_cache.get_stats()
        }

        # 收集缓存监控数据
        cache_monitor_stats = cache_monitor.get_current_metrics()

        return {
            "total_initialization_time": total_time,
            "cache_stats": cache_stats,
            "cache_monitor": cache_monitor_stats,
            "session_state_size": len(st.session_state),
            "llm_initialized": "llm" in st.session_state,
            "cache_health": self._check_cache_health()
        }

    def _check_cache_health(self) -> Dict:
        """检查缓存健康状态"""
        app_stats = self.app_cache.get_stats()
        auth_stats = self.auth_manager.auth_cache.get_stats()

        health_status = {
            "status": "healthy",
            "warnings": []
        }

        # 检查命中率
        if app_stats["hit_rate"] < 0.5:
            health_status["warnings"].append("应用缓存命中率较低")

        if auth_stats["hit_rate"] < 0.7:
            health_status["warnings"].append("认证缓存命中率较低")

        # 检查缓存使用率
        app_usage = app_stats["cache_size"] / app_stats["max_size"]
        if app_usage > 0.9:
            health_status["warnings"].append("应用缓存使用率过高")

        if health_status["warnings"]:
            health_status["status"] = "warning"

        return health_status

    def cleanup_caches(self):
        """清理所有缓存"""
        self.app_cache.clear()
        self.auth_manager.auth_cache.clear()
        global_cache.clear()
        logger.info("所有缓存已清理")

    def optimize_caches(self):
        """优化所有缓存"""
        # 清理过期项
        self.app_cache._cleanup_expired()
        self.auth_manager.auth_cache._cleanup_expired()
        global_cache._cleanup_expired()
        logger.info("缓存优化完成")


@measure_time
@error_handler
def optimized_main():
    """优化后的主程序"""
    # 创建初始化器
    initializer = OptimizedAppInitializer()
    
    # 启动缓存预热（后台进行）
    warmup_cache()
    
    # 检查会话状态
    initializer.check_session_expired()
    
    # 初始化基础配置
    app_config = initializer.initialize_basic_config()
    
    # 初始化数据库连接
    initializer.initialize_database()
    
    # 处理登录流程
    initializer.handle_login_flow(app_config)
    
    # 初始化会话数据
    initializer.initialize_session_data()
    
    # 懒加载LLM初始化
    initializer.initialize_llm_lazy()
    
    # 渲染侧边栏
    initializer.render_sidebar(app_config)
    
    # 处理菜单
    handle_menu(st.session_state.menu)
    
    # 在开发模式下显示初始化统计
    if settings.env == "dev":
        stats = initializer.get_initialization_stats()
        logger.info(f"应用初始化统计: {stats}")


if __name__ == "__main__":
    optimized_main()
