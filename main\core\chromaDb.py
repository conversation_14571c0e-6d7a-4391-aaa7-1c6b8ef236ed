#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: chromaDb.py 
@time: 2025/2/17 11:47
@Description: 
"""
from typing import Union

from langchain_chroma import Chroma

from core.documentLoader import DocumentLoader, TextSplitter
from config.config_center import settings
from core.myEmb import init_embedding


class VectorStoreManager:
    """向量存储管理类"""

    @staticmethod
    def init_chroma(
            file_path: Union[str, list],
            chunk_size: int = 500,
            chunk_overlap: int = 200
    ) -> Chroma:
        """初始化 Chroma 向量存储"""
        document_loader = DocumentLoader()
        text_splitter = TextSplitter()

        documents = document_loader.load_document(file_path)
        chunks = text_splitter.split_documents(
            documents,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )

        return Chroma.from_documents(
            persist_directory=settings.paths.CHROMADB_DIRECTORY,
            collection_name="questions",
            embedding=init_embedding(),
            documents=chunks
        )

    @staticmethod
    def init_chroma_from_existing() -> Chroma:
        """从现有数据初始化 Chroma"""
        return Chroma(
            persist_directory=settings.paths.CHROMADB_DIRECTORY,
            collection_name="questions",
            embedding_function=init_embedding()
        )
