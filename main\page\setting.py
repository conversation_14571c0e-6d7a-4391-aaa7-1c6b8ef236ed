#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: setting.py
@time: 2024/9/29 15:07
@Description:
"""

from typing import List, Dict, Optional, Tuple
import os
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

import pandas as pd
import streamlit as st

from config.config_center import settings
from common.utils import get_save_dir, save_uploaded_file
from core.milvusTool import init_milvus
from common.myLog import logger
from core.documentLoader import process_document_async

logger.name = __name__

# 常量定义
SUPPORTED_FILE_TYPES = ["pdf", "txt", "md", "csv", "docx"]
SUPPORTED_MIME_TYPES = {
    "application/pdf": "pdf",
    "text/plain": "txt",
    "text/markdown": "md",
    "text/csv": "csv",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx"
}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_CHUNK_SIZE = 1000
DEFAULT_CHUNK_OVERLAP = 200
UPLOAD_TIMEOUT = 30  # 上传超时时间（秒）

class FileValidator:
    @staticmethod
    def validate_file_size(file_size: int) -> bool:
        return file_size <= MAX_FILE_SIZE

    @staticmethod
    def validate_file_type(file_type: str) -> bool:
        return file_type in SUPPORTED_MIME_TYPES.values()

    @staticmethod
    def validate_filename_format(filename: str, sections: List[str]) -> Tuple[bool, List[str]]:
        try:
            name_parts = filename.split(".")[0].split("-")
            if len(name_parts) != 3:
                return False, []
            if name_parts[1] not in sections:
                return False, []
            return True, name_parts
        except Exception as e:
            logger.error(f"文件名验证错误: {str(e)}")
            return False, []

class SessionStateManager:
    @staticmethod
    def init_session_state():
        """初始化会话状态"""
        defaults = {
            "uploaded_files": [],
            "chunk_size": DEFAULT_CHUNK_SIZE,
            "chunk_overlap": DEFAULT_CHUNK_OVERLAP,
            "max_token": 2048,
            "temperature": 0.1,
            "searchType": "Rerank"
        }
        for key, value in defaults.items():
            if key not in st.session_state:
                st.session_state[key] = value

class FileHandler:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=3)

    async def handle_file_upload(self, file) -> Optional[str]:
        """异步处理文件上传"""
        try:
            if not FileValidator.validate_file_size(file.size):
                st.error("文件大小超过限制（10MB）")
                return None

            is_valid, name_parts = FileValidator.validate_filename_format(
                file.name,
                [i for i in settings.business.SECTION.values()]
            )
            
            if not is_valid:
                st.error("文件名格式不正确，请按照格式：文件名-部门-组")
                return None

            # 异步保存文件
            save_dir = get_save_dir()
            loop = asyncio.get_event_loop()
            file_path = await loop.run_in_executor(
                self.executor,
                save_uploaded_file,
                file,
                save_dir
            )

            return file_path
        except Exception as e:
            logger.error(f"文件上传处理错误: {str(e)}")
            st.error("文件上传失败，请重试")
            return None


class VectorDBHandler:
    @staticmethod
    async def process_vector_data(file_path: str, collection_name: str, chunk_size: int, chunk_overlap: int) -> bool:
        """异步处理向量数据"""
        try:
            datas = await process_document_async(file_path, chunk_size, chunk_overlap)
            await asyncio.to_thread(
                init_milvus().add_milvus_data,
                collection_name=collection_name,
                datas=datas
            )
            return True
        except Exception as e:
            logger.error(f"向量数据处理错误: {str(e)}")
            return False

async def init_setting():
    """初始化设置页面"""
    SessionStateManager.init_session_state()
    file_handler = FileHandler()
    
    st.header("模型设置", divider=settings.ui.header_divider)

    st.markdown("""
    <h5 style='color: red;'>🚨⚡️温馨提醒：修改本页内容后会影响大模型结果的准确性，请谨慎操作！⚡️🚨</h5>
    """, unsafe_allow_html=True)

    # 创建标签页
    llm_settings, know_upload = st.tabs(["大模型参数设置", "知识库上传"])

    # LLM 设置标签页内容
    with llm_settings:
        render_llm_settings()

    # 知识库上传标签页内容
    with know_upload:
        await render_knowledge_upload(file_handler)

def render_llm_settings():
    """渲染LLM设置界面"""
    st.session_state["searchType"] = st.selectbox(
        "选择检索方式",
        settings.llm.RETRIEVAL_LIST,
        index=3,
        help="Base为默认检索方式，单文档检索；MultiQuery为多重检索方式，多查询条件，单文档检索；Compression为压缩检索方式，单查询条件，单文档检索；Rerank为重排检索方式。"
    )

    st.session_state["max_token"] = st.number_input(
        "最大token设置",
        min_value=0,
        max_value=4096,
        value=st.session_state.max_token,
        help="输入最大token数，用于控制输出长度，避免生成过长文本"
    )

    st.session_state["temperature"] = st.slider(
        "温度设置",
        min_value=0.0,
        max_value=1.0,
        value=st.session_state.temperature,
        step=0.1,
        help="温度较低的值会产生更确定性的结果，较高的值会产生更多样化的结果"
    )

async def render_knowledge_upload(file_handler: FileHandler):
    """渲染知识库上传界面"""
    SECTION = [i for i in settings.business.SECTION.values()]
    
    st.subheader(
        "🚨上传的知识库文件，请按照该格式命名：文件名-部门-组",
        help=f"支持格式：{', '.join(SUPPORTED_FILE_TYPES)}。\n部门：{'、'.join(SECTION)}"
    )

    # 文件上传表单
    with st.form("upload-form", clear_on_submit=True):
        uploaded_file = st.file_uploader(
            "上传文件：",
            type=SUPPORTED_FILE_TYPES,
            accept_multiple_files=False,
            label_visibility="visible"
        )

        if submitted := st.form_submit_button(
                "上传",
                help="选择文件后点击上传按钮"
        ):
            if uploaded_file:
                with st.spinner(f"正在上传 {uploaded_file.name}..."):
                    file_path = await file_handler.handle_file_upload(uploaded_file)
                    if file_path:
                        update_session_state(uploaded_file, file_path)
                        st.toast('✔️ 文件上传成功！', icon='🎉')

    # 显示已上传文件
    render_uploaded_files()

    # 文本切分与向量化设置
    st.subheader("文本切分与向量化")
    await render_vector_settings(file_handler)

def update_session_state(file, file_path):
    """更新会话状态"""
    st.session_state.FN = file.name.split(".")[0]
    st.session_state.FS = st.session_state.FN.split("-")
    st.session_state.filePath = file_path
    st.session_state.uploaded_files.append({
        "name": file.name,
        "type": file.type,
        "size": file.size
    })

def render_uploaded_files():
    """渲染已上传文件列表"""
    if st.session_state.uploaded_files:
        with st.expander("已上传的知识库", expanded=True):
            df = pd.DataFrame(st.session_state.uploaded_files)
            st.dataframe(
                df,
                column_config={
                    "name": "文件名称",
                    "size": st.column_config.NumberColumn(
                        "文件大小",
                        format="%d byte"
                    ),
                    "type": "文件类型"
                },
                hide_index=True
            )
            
            if st.button("删除知识库", type="primary"):
                try:
                    os.remove(st.session_state.filePath)
                    st.session_state.uploaded_files = []
                    st.rerun()
                except Exception as e:
                    logger.error(f"删除文件错误: {str(e)}")
                    st.toast(f"删除文件失败: {str(e)}")

async def render_vector_settings(file_handler: FileHandler):
    """渲染向量设置界面"""
    with st.expander("参数设置", expanded=True):
        cols = st.columns(2)
        chunk_size = cols[0].number_input(
            "文本块大小",
            1,
            5000,
            st.session_state.chunk_size
        )
        chunk_overlap = cols[1].number_input(
            "重叠长度",
            0,
            5000,
            st.session_state.chunk_overlap
        )

        vector_group = st.selectbox(
            "选择向量组",
            ("技术中心-TQ", "产品中心-PQ", "业务中心-CSQ", "规章制度-AQ"),
            index=None,
            placeholder="请选择向量组"
        )

        if vector_group and st.button(
            "提交",
            disabled=len(st.session_state.uploaded_files) == 0,
            help="上传文件后，点击这里生成索引并保存到向量数据库"
        ):
            await process_vector_submission(
                vector_group,
                chunk_size,
                chunk_overlap,
                file_handler
            )

async def process_vector_submission(
    vector_group: str,
    chunk_size: int,
    chunk_overlap: int,
    file_handler: FileHandler
):
    """处理向量提交"""
    option_split = vector_group.split("-")
    
    with st.spinner("处理中...请勿重复提交"):
        try:
            success = await VectorDBHandler.process_vector_data(
                st.session_state.filePath,
                option_split[1],
                chunk_size,
                chunk_overlap
            )

            if success:
                # 更新知识库记录
                st.session_state.curd.add_knowledge(
                    document_name=st.session_state.FN,
                    section=st.session_state.FS[1],
                    group=st.session_state.FS[2],
                    relevance_ai=option_split[0],
                    create_name=st.session_state.userInfo.get("dt_name", "")
                )

                # 清理临时文件和状态
                cleanup_after_success()
                st.toast('✔️ 向量化处理完成！', icon='🎉')
                time.sleep(0.5)
                st.rerun()
            else:
                st.error("向量化处理失败，请重试")

        except Exception as e:
            logger.error(f"向量化处理错误: {str(e)}")
            st.error(f"处理失败: {str(e)}")

def cleanup_after_success():
    """成功后清理"""
    try:
        os.remove(st.session_state.filePath)
        st.session_state.uploaded_files = []
        st.session_state.FN = None
        st.session_state.FS = None
    except Exception as e:
        logger.error(f"清理文件错误: {str(e)}")