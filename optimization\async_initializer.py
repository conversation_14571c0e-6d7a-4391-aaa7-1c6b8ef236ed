#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: async_initializer.py
@time: 2025/7/24
@Description: 异步初始化器 - 优化启动性能
"""
import asyncio
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from functools import wraps

import streamlit as st
from common.myLog import logger


@dataclass
class InitTask:
    """初始化任务"""
    name: str
    func: Callable
    priority: int = 1  # 优先级，数字越大越优先
    timeout: float = 30.0
    required: bool = True
    dependencies: list = None


class AsyncInitializer:
    """异步初始化器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.tasks: Dict[str, InitTask] = {}
        self.results: Dict[str, Any] = {}
        self.errors: Dict[str, Exception] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
    def register_task(self, task: InitTask):
        """注册初始化任务"""
        self.tasks[task.name] = task
        logger.info(f"注册初始化任务: {task.name}")
    
    def _check_dependencies(self, task_name: str) -> bool:
        """检查任务依赖是否满足"""
        task = self.tasks[task_name]
        if not task.dependencies:
            return True
        
        for dep in task.dependencies:
            if dep not in self.results:
                return False
        return True
    
    def _execute_task(self, task: InitTask) -> Any:
        """执行单个任务"""
        start_time = time.time()
        try:
            logger.info(f"开始执行任务: {task.name}")
            result = task.func()
            execution_time = time.time() - start_time
            logger.info(f"任务 {task.name} 执行完成，耗时: {execution_time:.2f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"任务 {task.name} 执行失败，耗时: {execution_time:.2f}s，错误: {str(e)}")
            raise
    
    def run_parallel_init(self) -> Dict[str, Any]:
        """并行执行初始化任务"""
        # 按优先级排序任务
        sorted_tasks = sorted(
            self.tasks.values(), 
            key=lambda x: x.priority, 
            reverse=True
        )
        
        # 提交可执行的任务
        future_to_task = {}
        completed_tasks = set()
        
        while len(completed_tasks) < len(self.tasks):
            # 找到可以执行的任务
            ready_tasks = [
                task for task in sorted_tasks 
                if task.name not in completed_tasks 
                and task.name not in future_to_task.values()
                and self._check_dependencies(task.name)
            ]
            
            # 提交任务
            for task in ready_tasks:
                future = self.executor.submit(self._execute_task, task)
                future_to_task[future] = task.name
            
            # 等待任务完成
            if future_to_task:
                for future in as_completed(future_to_task, timeout=30):
                    task_name = future_to_task[future]
                    task = self.tasks[task_name]
                    
                    try:
                        result = future.result()
                        self.results[task_name] = result
                        completed_tasks.add(task_name)
                        logger.info(f"任务 {task_name} 完成")
                    except Exception as e:
                        self.errors[task_name] = e
                        if task.required:
                            logger.error(f"必需任务 {task_name} 失败: {str(e)}")
                            raise
                        else:
                            logger.warning(f"可选任务 {task_name} 失败: {str(e)}")
                            completed_tasks.add(task_name)
                    
                    # 清理已完成的future
                    del future_to_task[future]
            
            # 避免无限循环
            if not ready_tasks and future_to_task:
                time.sleep(0.1)
        
        return self.results


def background_init_decorator(task_name: str, priority: int = 1):
    """后台初始化装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查是否已经在后台初始化
            if f"bg_init_{task_name}" in st.session_state:
                return st.session_state[f"bg_init_{task_name}"]
            
            # 执行初始化
            result = func(*args, **kwargs)
            st.session_state[f"bg_init_{task_name}"] = result
            return result
        return wrapper
    return decorator


class LazyInitializer:
    """懒加载初始化器"""
    
    def __init__(self):
        self._initialized = {}
        self._initializers = {}
        self._lock = threading.Lock()
    
    def register(self, name: str, initializer: Callable):
        """注册懒加载初始化器"""
        self._initializers[name] = initializer
    
    def get(self, name: str):
        """获取初始化结果，如果未初始化则执行初始化"""
        if name not in self._initialized:
            with self._lock:
                if name not in self._initialized:
                    if name not in self._initializers:
                        raise ValueError(f"未注册的初始化器: {name}")
                    
                    logger.info(f"懒加载初始化: {name}")
                    start_time = time.time()
                    result = self._initializers[name]()
                    execution_time = time.time() - start_time
                    logger.info(f"懒加载 {name} 完成，耗时: {execution_time:.2f}s")
                    
                    self._initialized[name] = result
        
        return self._initialized[name]
    
    def is_initialized(self, name: str) -> bool:
        """检查是否已初始化"""
        return name in self._initialized


# 全局实例
async_initializer = AsyncInitializer()
lazy_initializer = LazyInitializer()


def create_optimized_init_tasks():
    """创建优化的初始化任务"""
    from common.initialize import init_curd, init_llm, init_data
    
    # 配置初始化任务
    tasks = [
        InitTask(
            name="config",
            func=lambda: None,  # 配置已在导入时加载
            priority=10,
            timeout=5.0,
            required=True
        ),
        InitTask(
            name="database",
            func=init_curd,
            priority=9,
            timeout=10.0,
            required=True,
            dependencies=["config"]
        ),
        InitTask(
            name="session_data",
            func=init_data,
            priority=8,
            timeout=5.0,
            required=True
        ),
        InitTask(
            name="llm",
            func=init_llm,
            priority=5,
            timeout=30.0,
            required=False,  # LLM可以延迟初始化
            dependencies=["database"]
        )
    ]
    
    for task in tasks:
        async_initializer.register_task(task)
    
    return async_initializer
