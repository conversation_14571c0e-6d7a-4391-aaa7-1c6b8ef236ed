#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: ReactAgent.py
@time: 2024/12/10 10:26
@Description:
"""
import os
from typing import List, Dict, Any
from dataclasses import dataclass
from functools import lru_cache

from dotenv import load_dotenv
from BCEmbedding.tools.langchain import BCERerank
from langchain.retrievers import ContextualCompressionRetriever
from langchain_core.prompts import PromptTemplate
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_community.utilities import SerpAPIWrapper
from langchain.agents import tool
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain.agents import create_react_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langfuse.callback import CallbackHandler

from core.milvusTool import MilvusService
from config.config_center import settings

load_dotenv()

@dataclass
class AgentConfig:
    google_api_key: str = os.getenv("GOOGLE_API_KEY")
    base_url: str = os.environ.get("BASE_URL")
    api_key: str = os.environ.get("API_KEY")
    model_name: str = os.environ.get("MODEL")
    rerank_model: str = settings.paths.RERANK_MODEL_PATH
    MEMORY_KEY: str = "chat_history"
    REDIS_URL: str = settings.paths.REDIS_URL
    cuda_enabled: bool = False

class VectorStoreManager:

    def __init__(self):
        self.milvus_service = MilvusService()
        self._vector_stores: Dict[str, Any] = {}
    
    @lru_cache(maxsize=4)
    def get_retriever(self, collection_name: str, k: int = 10) -> VectorStoreRetriever:
        if collection_name not in self._vector_stores:
            vectorstore = self.milvus_service.initialize_milvus(collection_name)
            self._vector_stores[collection_name] = vectorstore
        return self._vector_stores[collection_name].as_retriever(search_kwargs={"k": k})

class Reranker:

    def __init__(self, config: AgentConfig):
        self.config = config
        self._reranker = None
    
    def create_compression_retriever(
        self, 
        retriever: VectorStoreRetriever
    ) -> ContextualCompressionRetriever:
        return ContextualCompressionRetriever(
            base_retriever=retriever,
            base_compressor=BCERerank(
                model=self.config.rerank_model,
                top_n=5,
                device="cuda:0" if self.config.cuda_enabled else "cpu"
            )
        )

class ToolManager:
    
    def __init__(self, config: AgentConfig, vector_store_manager: VectorStoreManager, reranker: Reranker):
        self.config = config
        self.vector_store_manager = vector_store_manager
        self.reranker = reranker
        self._tools = None
    
    @property
    def tools(self) -> List[Any]:
        if self._tools is None:
            self._tools = [
                self._create_search_tool(),
                self._create_csq_retriever(),
                self._create_aq_retriever(),
                self._create_tq_retriever(),
                self._create_pq_retriever()
            ]
        return self._tools
    
    def _create_search_tool(self):
        @tool
        def search_tool(query: str) -> str:
            """
            使用SerpAPI搜索引擎工具进行网络搜索。适用于：
            1. 查询实时信息，如天气、新闻、股票等
            2. 获取地理位置信息，如地址、路线等
            3. 查找最新的产品信息、价格等
            4. 搜索一般性的知识性问题

            输入格式：直接输入要搜索的问题或关键词
            示例：
            - "上海今天的天气怎么样？"
            - "苹果公司最新股价"
            - "北京到上海的航班时刻表"
            """
            search = SerpAPIWrapper(serpapi_api_key=self.config.google_api_key)
            return search.run(query)
        return search_tool
    
    def _create_csq_retriever(self):
        @tool
        def retriever_csq_tool(query: str) -> str:
            """
            约苗常见问题知识库检索工具。这是查询约苗系统相关问题的首选工具。

            适用场景：
            1. 约苗系统操作相关问题
               - 预约信息修改、取消、查询
               - 用户注册、登录、密码重置
               - 个人信息管理
            2. 约苗业务流程问题
               - 预约流程说明
               - 支付相关问题
               - 退款处理流程
            3. 系统使用指南
               - 功能操作说明
               - 常见错误处理
               - 系统使用技巧
            4. 故障排除
               - 系统报错处理
               - 功能异常解决
               - 网络连接问题

            输入格式：直接输入约苗系统相关的问题
            示例：
            - "如何修改预约信息？"
            - "约苗系统登录失败怎么办？"
            - "如何查看历史预约记录？"
            - "预约后如何取消？"
            - "忘记密码怎么重置？"

            注意：此工具专门用于查询约苗系统相关的所有问题，包括但不限于预约、用户管理、系统操作等。
            """
            retriever = self.vector_store_manager.get_retriever("CSQ")
            compression_retriever = self.reranker.create_compression_retriever(retriever)
            return compression_retriever.invoke(query)
        return retriever_csq_tool
    
    def _create_aq_retriever(self):
        @tool
        def retriever_aq_tool(query: str) -> str:
            """
            马太科技规章制度检索工具。适用于：
            1. 查询公司各项规章制度的具体内容
            2. 了解公司政策、流程和规范
            3. 获取人事、行政等相关规定
            4. 查找公司管理制度的实施细则
            
            输入格式：直接输入要查询的制度名称或关键词
            示例：
            - "员工考勤制度"
            - "报销流程规定"
            - "年假申请流程"
            """
            retriever = self.vector_store_manager.get_retriever("AQ")
            compression_retriever = self.reranker.create_compression_retriever(retriever)
            return compression_retriever.invoke(query)
        return retriever_aq_tool
    
    def _create_tq_retriever(self):
        @tool
        def retriever_tq_tool(query: str) -> str:
            """
            马太科技技术文档检索工具。适用于：
            1. 查询公司技术架构文档
            2. 获取API接口文档
            3. 查找开发规范和指南
            4. 了解系统设计文档
            5. 获取技术问题解决方案
            
            输入格式：直接输入技术相关的问题或关键词
            示例：
            - "用户认证接口文档"
            - "数据库设计规范"
            - "系统部署流程"
            """
            retriever = self.vector_store_manager.get_retriever("TQ")
            compression_retriever = self.reranker.create_compression_retriever(retriever)
            return compression_retriever.invoke(query)
        return retriever_tq_tool
    
    def _create_pq_retriever(self):
        @tool
        def retriever_pq_tool(query: str) -> str:
            """
            马太科技产品规则检索工具。适用于：
            1. 查询产品功能规则
            2. 获取业务流程规范
            3. 了解产品使用限制
            4. 查找产品配置说明
            5. 获取产品更新说明
    
            输入格式：直接输入产品相关的问题或关键词
            示例：
            - "用户注册规则"
            - "订单处理流程"
            - "产品功能限制说明"
            """
            retriever = self.vector_store_manager.get_retriever("PQ")
            compression_retriever = self.reranker.create_compression_retriever(retriever)
            return compression_retriever.invoke(query)
        return retriever_pq_tool

class AgentManager:

    def __init__(self, config: AgentConfig):
        self.config = config
        self.llm = self._getLLM
        self.vector_store_manager = VectorStoreManager()
        self.reranker = Reranker(config)
        self.tool_manager = ToolManager(config, self.vector_store_manager, self.reranker)
        self._tools = self.tool_manager.tools
        self._agent_executor = None
        self._prompt = None

    @property
    def _getLLM(self):
        return ChatOpenAI(
            openai_api_base=self.config.base_url,
            openai_api_key=self.config.api_key,
            model_name=self.config.model_name,
            temperature=0.5
        )

    @property
    def prompt(self) -> PromptTemplate:
        if self._prompt is None:
            self._prompt = PromptTemplate.from_template("""
            Assistant is a large language model trained by OpenAI.
            Assistant is designed to be able to assist with a wide range of tasks, from answering simple questions to providing in-depth explanations and discussions on a wide range of topics. As a language model, Assistant is able to generate human-like text based on the input it receives, allowing it to engage in natural-sounding conversations and provide responses that are coherent and relevant to the topic at hand.
            Assistant is constantly learning and improving, and its capabilities are constantly evolving. It is able to process and understand large amounts of text, and can use this knowledge to provide accurate and informative responses to a wide range of questions. Additionally, Assistant is able to generate its own text based on the input it receives, allowing it to engage in discussions and provide explanations and descriptions on a wide range of topics.
            Overall, Assistant is a powerful tool that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. Whether you need help with a specific question or just want to have a conversation about a particular topic, Assistant is here to assist.

            IMPORTANT: When the user asks about 约苗 system operations,预约 information, or system-related questions, you MUST use the CSQ retriever tool first. This is the primary tool for handling 约苗 system queries.

            TOOLS:
            ------
            Assistant has access to the following tools:
            {tools}
            To use a tool, please use the following format:
            ```
            Thought: Do I need to use a tool? Yes
            Action: the action to take, should be one of [{tool_names}]
            Action Input: the input to the action
            Observation: the result of the action
            ```
            When you have a response to say to the Human, or if you do not need to use a tool, you MUST use the format:
            ```
            Thought: Do I need to use a tool? No
            Final Answer: [your response here]
            ```
            Begin!
            Previous conversation history:
            {chat_history}
            New input: {input}
            {agent_scratchpad}
            """)
        return self._prompt
    
    @property
    def agent_executor(self) -> RunnableWithMessageHistory:
        if self._agent_executor is None:
            agent = create_react_agent(self.llm, self._tools, self.prompt)
            executor = AgentExecutor(
                agent=agent,
                tools=self._tools,
                verbose=True,
                max_iterations=3,
                early_stopping_method="generate"
            )
            self._agent_executor = RunnableWithMessageHistory(
                executor,
                lambda session_id: RedisChatMessageHistory(
                    session_id=session_id,
                    url=self.config.REDIS_URL,
                    key_prefix=self.config.MEMORY_KEY,
                    ttl=3600
                ),
                input_messages_key="input",
                history_messages_key="chat_history",
            )
        return self._agent_executor
    
    def process_query(self, query: str, session_id: str = "default") -> str:
        output = self.agent_executor.invoke(
            {"input": query},
            config={
                "configurable": {"session_id": session_id},
                "callbacks": [CallbackHandler()],
                "metadata": {
                    "langfuse_user_id": session_id,
                }
            }
        )
        return output.get("output", "")

def main():
    config = AgentConfig()
    agent_manager = AgentManager(config)
    
    # Example usage
    query = "约苗公号无法打开"
    response = agent_manager.process_query(query, session_id="zq")
    print(response)

if __name__ == "__main__":
    main() 