#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: document.py
@time: 2025/2/14 15:24
@Description:
"""

import streamlit as st

from core.documentLoader import loader_document
from core.chain import ChainFactory
from core.prompt import prompt_manager
from common.utils import stream_output_history, get_system_avatar, remove_empty_lines
from common.myLog import logger
from page.setting import FileHandler
from config.config_center import settings

logger.name = __name__

class DocumentAnalysisService:
    def __init__(self):
        self.chain_factory = ChainFactory()

    @staticmethod
    def detect_encoding(
        file_content: bytes
    ) -> str:
        encodings = ['utf-8', 'gbk', 'gb18030', 'iso-8859-1', 'big5']
        for encoding in encodings:
            try:
                file_content.decode(encoding)
                return encoding
            except UnicodeDecodeError:
                continue
        return 'utf-8'

    def analyze_document(
        self,
        content: str,
        analysis_type: str = "chat",
        chat_type: str = None
    ) -> str:
        try:
            logger.info(f"Document analysis_type: {analysis_type}", )
            chain = self.chain_factory.create_chain(
                chain_type="history",
                prompt=prompt_manager.history_prompt(
                    name=analysis_type,
                    system_message="你是一名专业的文档分析师"
                ),
                session_state=st.session_state
            )

            response = stream_output_history(
                model=chain,
                input_message=content,
                session_id=st.session_state.userInfo["dt_userid"],
                ai_type="N" if chat_type else "Y",
                doc_content=st.session_state.doc_content if chat_type else None
            )

            return response
        except Exception as e:
            logger.error(f"Document analysis error: {str(e)}")
            raise

class DocumentAnalysisView:
    def __init__(self, service: DocumentAnalysisService):
        self.service = service
        self._init_session_state()
        self.file_handler = FileHandler()

    @staticmethod
    def _init_session_state():
        if "doc_content" not in st.session_state:
            st.session_state.doc_content = ""
        if "analysis_type" not in st.session_state:
            st.session_state.analysis_type = None
        if "document_messages" not in st.session_state:
            st.session_state.document_messages = [
                {
                    "role": "assistant",
                    "avatar": get_system_avatar(),
                    "content": "您好！我是 :red[文档分析]智能助手。您只需提供文档，我就能快速为您完成专业的文档分析。"
                }
            ]

    async def render_uploader(self):
        with st.expander("📁 文档上传", expanded=True):
            uploaded_file = st.file_uploader(
                "上传文档",
                type=['txt', 'md', 'doc', 'docx', 'pdf'],
                help="支持多种文档格式，最大100MB"
            )
            text_content = st.text_area(
                "请输入文档内容",
                placeholder="请输入文档内容",
                key="text_content"
            )
        if uploaded_file:
            self._handle_file_upload(uploaded_file)
        elif text_content:
            st.session_state.doc_content = text_content
        else: st.session_state.doc_content = ""

        if st.button("上传文档", disabled=not uploaded_file):
            file_path = await self.file_handler.handle_file_upload(uploaded_file)
            logger.info(f"Document uploaded: {file_path}")
            # documents = await loader_document(file_path)
            # print(documents)
            st.toast('✔️ 文件上传成功！', icon='🎉')

    @staticmethod
    def render_analysis_type():
        analysis_types = {
            "requirement_prompt": "需求文档",
            "technical_prompt": "技术文档",
            "general_prompt": "通用文档"
        }
        selected = st.selectbox(
            "选择分析类型",
            options=list(analysis_types.keys()),
            format_func=lambda x: analysis_types[x],
            index=None,
            placeholder="请选择文档类型"
        )
        if selected:
            st.session_state.analysis_type = selected
        else: st.session_state.analysis_type = None

    def _handle_file_upload(self, uploaded_file):
        try:
            if uploaded_file.size > 100 * 1024 * 1024:
                st.error("文件大小超过100MB限制")
                return

            content = uploaded_file.read()
            encoding = self.service.detect_encoding(content)
            decoded_content = content.decode(encoding)
            st.session_state.doc_content = decoded_content

            with st.expander("📜 原始文档预览"):
                st.text(decoded_content[:5000] + ("..." if len(decoded_content) > 5000 else ""))

        except Exception as e:
            logger.error(f"文件处理失败: {str(e)}")
            st.error(f"文件处理失败: {str(e)}")

    def render_analysis_controls(self):
        if st.button("🔍 开始分析", type="primary", disabled=not st.session_state.doc_content):
            self._run_analysis()


    def _run_analysis(self):
        if not st.session_state.analysis_type:
            st.error("请先选择分析类型")
            st.stop()

        with st.spinner("🔎 深度解析中..."):
            try:
                response = self.service.analyze_document(
                    st.session_state.doc_content,
                    st.session_state.analysis_type
                )

                with st.expander("分析报告", expanded=True, icon="📊"):
                    st.write_stream(response)
            except Exception as e:
                st.error(f"分析失败: {str(e)}")

    def chat_with_llm(self,) -> None:

        for message in st.session_state.document_messages:
            with st.chat_message(message["role"], avatar=message.get("avatar", None)):
                st.markdown(message["content"])

        if prompt := st.chat_input("请输入你的问题，按Enter键发送。"):

            if not st.session_state.doc_content:
                st.error("请先输入或上传文档！")
                st.stop()

            prompt = remove_empty_lines(prompt)

            with st.chat_message("user"):
                st.markdown(prompt)

            st.session_state.document_messages.append({"role": "user", "content": prompt})

            with st.chat_message("assistant", avatar=get_system_avatar()):
                # 获取 AI 响应
                with st.spinner("🤔 AI助手正在深度思考，请稍作等待..."):

                    rps = self.service.analyze_document(
                        prompt,
                        analysis_type="chat_prompt",
                        chat_type="N"
                    )

                    response = st.write_stream(rps)

                    # 添加 AI 响应
                    st.session_state.document_messages.append({
                        "role": "assistant",
                        "avatar": get_system_avatar(),
                        "content": response
                    })


async def init_document_analysis():
    service = DocumentAnalysisService()
    view = DocumentAnalysisView(service)

    st.markdown(
        f"""### 文档<span style='color: {settings.ui.global_color}'>智能</span>分析助手""",
        unsafe_allow_html=True
    )
    st.divider()

    left, right = st.columns([2, 3])

    with left:
        st.subheader("文档分析")

        await view.render_uploader()
        view.render_analysis_type()
        view.render_analysis_controls()

    with right:
        st.subheader("交互式分析")

        if st.button("🔄 重置会话", type="secondary"):
            st.session_state.document_messages = []

        view.chat_with_llm()