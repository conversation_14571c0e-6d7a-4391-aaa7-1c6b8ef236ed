#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: initialize.py 
@time: 2024/9/29 15:07
@Description: 
"""
import json
import streamlit as st

from common.dingTalk import DingTalkAPI, DingTalkAPIError
from common.myLog import logger
from model.curd import CRUD

CACHE_TIME: int = 3600
logger.name = __name__


def init_data():
    if "llmType" not in st.session_state:
        st.session_state["llmType"] = "ZhiPuAi-glm4"
        st.session_state["llmTypeIndex"] = 1

    if "searchType" not in st.session_state:
        st.session_state["searchType"] = "Rerank"
        st.session_state["searchTypeIndex"] = 3

    if 'uploaded_files' not in st.session_state:
        st.session_state.uploaded_files = []

    if "chunk_size" not in st.session_state:
        st.session_state.chunk_size = 500

    if "chunk_overlap" not in st.session_state:
        st.session_state.chunk_overlap = 200

    if "max_token" not in st.session_state:
        st.session_state.max_token = 0

    if "temperature" not in st.session_state:
        st.session_state.temperature = 0.1

    if "filePath" not in st.session_state:
        st.session_state.filePath = None

    if "collection_name" not in st.session_state:
        st.session_state.collection_name = None

    if "FN" not in st.session_state:
        st.session_state.FN = None

    if "FS" not in st.session_state:
        st.session_state.FS = None

    logger.info(f"init_data successfully")

@st.cache_data(ttl=CACHE_TIME)
def Login(qr_code: dict) -> None:
    if isinstance(qr_code, dict):
        try:
            dingTalk = DingTalkAPI(qr_code["authCode"])
            userInfo = dingTalk.get_user_info()
            if userInfo:
                st.session_state.curd.add_user(**userInfo)
                st.session_state.userInfo = st.session_state.curd.get_user_info(**userInfo)
        except Exception as e:
            logger.warning(f"{e}")


@st.cache_resource(ttl=CACHE_TIME)
def init_curd():
    return CRUD()


def init_llm():
    configs = st.session_state.curd.getConfigValues(["llm", "llm_comm", "emb", "search_type", "base_k", "top_k"])
    st.session_state.llm = json.loads(configs["llm"])
    st.session_state.llm_comm = json.loads(configs["llm_comm"])
    st.session_state.emb = json.loads(configs["emb"])
    st.session_state.search_type = configs["search_type"]
    st.session_state.base_k = configs["base_k"]
    st.session_state.top_k = configs["top_k"]
    logger.info(f"init_llm successfully")