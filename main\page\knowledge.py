#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: knowledge.py
@time: 2024/9/29 15:07
@Description:
"""

from typing import List, Dict, Any
from collections import OrderedDict

import pandas as pd
import streamlit as st
from pandas import DataFrame

from config.config_center import settings
from common.utils import SerializedData
from const.constants import KNOWLEDGE_TABLE_COLUMNS
from common.myLog import logger

logger.name = __name__

class KnowledgeManager:
    """知识库文档管理类"""
    
    def __init__(self):
        self.column_order = list(OrderedDict(KNOWLEDGE_TABLE_COLUMNS))

    @staticmethod
    async def fetch_knowledge_data() -> List[Dict[str, Any]]:
        """获取知识库数据
        
        Returns:
            List[Dict[str, Any]]: 知识库数据列表
        
        Raises:
            DataFetchError: 数据获取失败时抛出
        """
        try:
            return SerializedData(st.session_state.curd.get_knowledge)
        except Exception as e:
            logger.error(f"获取知识库数据失败: {str(e)}")
            raise

    @staticmethod
    def create_knowledge_dataframe(data: List[Dict[str, Any]]) -> DataFrame:
        """创建知识库数据框
        
        Args:
            data: 知识库原始数据
            
        Returns:
            DataFrame: 处理后的数据框
        """
        return pd.DataFrame(data)

    def display_knowledge_table(self, df: DataFrame) -> None:
        """显示知识库数据表格
        
        Args:
            df: 要显示的数据框
        """
        st.dataframe(
            df,
            column_config=KNOWLEDGE_TABLE_COLUMNS,
            column_order=self.column_order,
            use_container_width=True,
            hide_index=True
        )

async def init_knowledge() -> None:
    """初始化知识库文档页面"""
    try:
        st.header("知识库", divider=settings.ui.header_divider)
        
        doc_manager = KnowledgeManager()
        knowledge_data = await doc_manager.fetch_knowledge_data()
        knowledge_df = doc_manager.create_knowledge_dataframe(knowledge_data)
        doc_manager.display_knowledge_table(knowledge_df)
        logger.info("知识库加载成功")
        
    except Exception as e:
        logger.error(f"知识库初始化失败: {str(e)}")
        st.error("知识库加载失败，请稍后重试")