#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: de.py 
@time: 2025/3/4 11:17
@Description: 
"""
from typing import TypedDict, List, Optional

from dotenv import load_dotenv
from langgraph.graph import END, StateGraph
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_community.document_loaders import WebBaseLoader, TextLoader
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field, ValidationError
import os

load_dotenv()

# 增强版状态定义
class AgentState(TypedDict):
    input_content: str
    parsed_requirements: Optional[dict]
    test_cases: List[dict]
    validation_result: Optional[dict]
    error_info: Optional[str]


# 重新设计的数据模型（增加字段别名和校验规则）
class TestCase(BaseModel):
    case_id: str = Field(..., alias="用例编号", min_length=5, description="TC-001格式的用例标识")
    description: str = Field(..., alias="用例描述", min_length=10)
    preconditions: List[str] = Field(default_factory=list, alias="前置条件")
    steps: List[str] = Field(..., alias="操作步骤", min_items=1)
    expected_result: str = Field(..., alias="预期结果", min_length=5)
    test_type: str = Field("功能测试", alias="测试类型", pattern="功能测试|性能测试|安全测试|兼容性测试")
    priority: str = Field("中", alias="优先级", pattern="高|中|低")

    class Config:
        allow_population_by_field_name = True


class RequirementAnalysis(BaseModel):
    key_features: List[str] = Field(..., alias="核心功能")
    user_scenarios: List[str] = Field(..., alias="用户场景", description="包含正常和异常场景")
    edge_cases: List[str] = Field(..., alias="边界条件")
    business_rules: List[str] = Field(default_factory=list, alias="业务规则")

    class Config:
        allow_population_by_field_name = True

def getLlm(stream: bool = True) -> ChatOpenAI:
    return ChatOpenAI(
        openai_api_base=os.environ.get("BASE_URL"),
        openai_api_key=os.environ.get("API_KEY"),
        model_name=os.environ.get("MODEL"),
        temperature=0.1,
        streaming=stream,
        max_retries=3,
        request_timeout=30
    )

llm = getLlm()


# 改进后的输入处理节点
def input_processing_node(state: AgentState):
    try:
        user_input = state["input_content"]
        if user_input.startswith(("http://", "https://")):
            loader = WebBaseLoader(user_input)
        else:
            if not os.path.exists(user_input):
                raise FileNotFoundError(f"文件 {user_input} 不存在")
            loader = TextLoader(user_input, encoding="utf-8")

        docs = loader.load()
        print('----------------->', docs)
        return {"input_content": docs[0].page_content}
    except Exception as e:
        return {"error_info": f"输入处理失败: {str(e)}"}


# 防御性编程的需求分析节点
def requirement_analysis_node(state: AgentState):
    analysis_parser = JsonOutputParser(pydantic_object=RequirementAnalysis)

    prompt = ChatPromptTemplate.from_template("""
    请严格按照以下JSON格式输出需求分析结果：
    {{
      "核心功能": ["功能1", "功能2", "功能3"],
      "用户场景": ["场景1", "场景2"],
      "边界条件": ["条件1", "条件2"],
      "业务规则": ["规则1", "规则2"]
    }}

    待分析需求：
    {requirements}
    
    输出格式：
    {format}
    """)

    chain = prompt | llm | analysis_parser

    try:
        analysis = chain.invoke({
            "requirements": state["input_content"]
        })
        return {
            "parsed_requirements": analysis.invoke({
                "requirements": state["input_content"],
                "format": analysis_parser.get_format_instructions()
            })
        }

    except ValidationError as e:
        print(f"需求分析验证错误: {e}")
        return {"error_info": f"需求分析失败: {str(e)}"}


# 带验证重试的测试用例生成节点
def test_case_generation_node(state: AgentState):
    case_parser = JsonOutputParser(pydantic_object=TestCase)

    prompt = ChatPromptTemplate.from_template("""
    请严格按照以下格式生成测试用例：
    [
      {{
        "用例编号": "TC-001",
        "用例描述": "测试案例描述...",
        "前置条件": ["条件1", "条件2"],
        "操作步骤": ["步骤1", "步骤2"],
        "预期结果": "预期达到的效果...",
        "测试类型": "功能测试",
        "优先级": "高"
      }}
    ]

    需求分析结果：
    {requirements_analysis}

    设计要求：
    1. 覆盖所有核心功能（正反向测试）
    2. 包含边界值测试（BVC）用例
    3. 包含异常输入测试用例
    4. 包含界面操作性测试
    5. 包含关键业务流程测试
    6. 不同优先级分布（高:30%/中:50%/低:20%）

    测试用例要素：
    - 需体现前置条件
    - 验证步骤应包含具体参数
    - 预期结果需量化可验证
    - 包含必要的测试数据说明
    
    输出格式：
    {format}
    """)

    chain = prompt | llm | case_parser

    try:
        test_cases = chain.invoke({
            "requirements_analysis": state["parsed_requirements"]
        })

        return {
            "test_cases": test_cases.invoke({
                "requirements_analysis": state["parsed_requirements"],
                "format": case_parser.get_format_instructions()
            })
        }

    except ValidationError as e:
        print(f"测试用例结构错误: {e}")
        return {"error_info": f"用例生成失败: {str(e)}"}


# 增强的安全验证节点
def quality_validation_node(state: AgentState):
    prompt = ChatPromptTemplate.from_template("""
    验证以下测试案例是否符合质量要求：
    {test_cases}

    验收标准：
    1. 所有操作步骤必须具体可执行
    2. 预期结果需包含可量化的验证指标
    3. 需包含至少10%的安全测试用例
    4. 无重复测试用例

    返回格式：
    {{
      "is_valid": boolean,
      "feedback": "改进建议文本",
      "problem_count": 数量
    }}
    """)

    chain = prompt | llm | JsonOutputParser()
    return {
        "validation_result": chain.invoke({"test_cases": state["test_cases"]})
    }


# 新增的验证节点
def requirement_validation_node(state: AgentState):
    try:
        RequirementAnalysis(**state["parsed_requirements"])
        return {"req_validation": "pass"}
    except ValidationError as e:
        print(f"需求分析验证失败: {e}")
        return {"req_validation": "fail", "error_info": str(e)}


# 构建稳定的工作流
workflow = StateGraph(AgentState)

# 新增节点注册
nodes = [
    ("process_input", input_processing_node),
    ("analyze_req", requirement_analysis_node),
    ("validate_req", requirement_validation_node),
    ("generate_tc", test_case_generation_node),
    ("validate_quality", quality_validation_node)
]

for node in nodes:
    workflow.add_node(*node)

# 工作流连接（带错误重试）
workflow.set_entry_point("process_input")

workflow.add_edge("process_input", "analyze_req")
workflow.add_edge("analyze_req", "validate_req")

# 需求验证条件分支
workflow.add_conditional_edges(
    "validate_req",
    lambda s: "pass" if s.get("req_validation") == "pass" else "retry",
    {
        "pass": "generate_tc",
        "retry": "analyze_req"
    }
)

workflow.add_edge("generate_tc", "validate_quality")


# 质量标准验证分支
def quality_check(state):
    return "accept" if state["validation_result"]["is_valid"] else "revise"


workflow.add_conditional_edges(
    "validate_quality",
    quality_check,
    {
        "accept": END,
        "revise": "generate_tc"
    }
)

app = workflow.compile()

# 测试执行示例
if __name__ == "__main__":
    test_input = r"D:\Work\code\ym_agent\main\documents\fileData.txt"

    try:
        result = app.invoke({"input_content": test_input})

        if result.get("error_info"):
            print(f"执行失败: {result['error_info']}")
        else:
            print("生成的测试用例：")
            for case in result["test_cases"]:
                validated = TestCase(**case)
                print(f"[{validated.case_id}] {validated.description}")
                print(f"优先级：{validated.priority} 类型：{validated.test_type}")
                print("步骤：")
                for i, step in enumerate(validated.steps, 1):
                    print(f" {i}. {step}")
                print()

    except Exception as e:
        print(f"工作流执行异常: {str(e)}")
