#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: chat.py
@time: 2024/10/14 16:24
@Description:
"""

import asyncio

import streamlit as st
import time
from typing import TypedDict, Optional, Deque
from dataclasses import dataclass
from collections import deque
from typing import Dict, Any

from common.myLog import logger
from common.utils import stream_output, get_system_avatar, remove_empty_lines
from config.config_center import settings
from const.enums import QueryRuleEnum
from const.cache import cache
from agent.AgenticRag import WorkflowEngine

logger.name = __name__

class Message(TypedDict):
    """消息类型定义"""
    role: str
    avatar: Optional[str]
    content: str


@dataclass
class ChatConfig:
    """聊天配置类"""
    max_history: int = 50
    default_temperature: float = 0.7
    max_retries: int = 3
    timeout: int = 30
    system_avatar: str = "SYSTEM_LOG0"


class ChatHistory:
    """聊天历史管理类"""
    def __init__(self, max_size: int = 50):
        self.messages: Deque[Message] = deque(maxlen=max_size)
    
    def add_message(self, message: Message) -> None:
        """添加消息到历史记录"""
        self.messages.append(message)
    
    def clear_history(self) -> None:
        """清空历史记录"""
        self.messages.clear()
    
    def get_messages(self) -> list[Message]:
        """获取所有消息"""
        return list(self.messages)

class ChatManager:
    """聊天管理类"""

    @staticmethod
    async def process_llm_response(llm: Any, msg: str) -> str:
        try:
            rps = stream_output(llm, msg)
            response = st.write_stream(rps)
            return response
        except Exception as e:
            logger.error(f"处理LLM响应时出错: {str(e)}")
            raise

    @staticmethod
    async def update_chat_history(
        msg: str,
        user_info: Dict[str, Any],
        response: str,
        active: str,
        query_time: float,
        curd: Any
    ) -> None:
        """更新聊天历史"""
        try:
            # 保存用户消息到数据库
            curd.add_message(
                role=QueryRuleEnum.user.value,
                content=msg,
                dt_userid=user_info["dt_userid"],
                dt_name=user_info["dt_name"],
                type=active
            )

            # 更新会话历史记录
            st.session_state.history_messages.append(
                {
                    "role": "assistant",
                    "avatar": get_system_avatar(),
                    "content": response
                }
            )
            
            # 保存AI消息到数据库
            curd.add_message(
                role=QueryRuleEnum.ai.value,
                content=response,
                dt_userid="",
                dt_name="",
                type=active
            )
            
            logger.info(f"聊天历史已更新. 耗时: {query_time}s")
        except Exception as e:
            logger.error(f"更新聊天历史失败: {str(e)}")
            raise

def init_session_state(active: str = "TY") -> None:
    """初始化会话状态"""
    default_states = {
        "history_messages": [
            {
                "role": "assistant",
                "avatar": get_system_avatar(),
                "content": settings.business.Q_TEMPLATE.get(active)
            }
        ],
        "last_active": None,
        "chat_manager": ChatManager()
    }
    
    for key, default_value in default_states.items():
        if key not in st.session_state:
            st.session_state[key] = default_value

async def handle_user_message(
        prompt: str
) -> None:
    """处理用户消息"""
    try:
        msg = remove_empty_lines(prompt)

        with st.chat_message("user"):
            st.markdown(remove_empty_lines(msg))

        st.session_state.history_messages.append({"role": "user", "content": msg})

    except Exception as e:
        logger.error(f"处理用户消息时出错: {str(e)}")
        raise

async def new_init_chat() -> None:
    """初始化聊天界面"""
    try:
        # 设置页面标题
        st.markdown(
            f"""### 约苗<span style='color: {settings.ui.global_color}'>智能</span>问答助手""",
            unsafe_allow_html=True
        )
        st.divider()

        # 初始化会话状态
        init_session_state("TY")

        # 显示历史消息
        for message in st.session_state.history_messages:
            with st.chat_message(message["role"], avatar=message.get("avatar", None)):
                st.markdown(message["content"])

        # 处理用户输入
        if msg := st.chat_input("请输入你的问题，按Enter键发送。"):

            await handle_user_message(msg)

            with st.chat_message("assistant", avatar=get_system_avatar()):
                with st.spinner("请耐心等待，AI 助手正在全力思考您的问题..."):
                    start_time = time.time()
                    
                    response_container = st.empty()
                    full_response = ""
                    target_collection = ""

                    workflow = WorkflowEngine(
                        session_state=cache.get_all(),
                        thread_id=st.session_state.userInfo["dt_userid"]
                    )

                    try:
                        async for chunk, active in workflow.astream_high_performance(msg):
                            if chunk:
                                full_response += chunk
                                target_collection = active

                                with response_container:
                                    st.markdown(full_response + "▌")

                                await asyncio.sleep(0.01)

                    except Exception as e:
                        error_msg = f"生成回答时出现错误: {str(e)}"
                        logger.error(error_msg, exc_info=True)
                        with response_container:
                            st.error(error_msg)
                        return

                    # 最终显示
                    with response_container:
                        st.markdown(full_response)

                    query_time = round(time.time() - start_time, 2)
                    st.caption(f"⏱️耗时：{query_time} second(s)")
                    
                    # 更新聊天历史
                    await ChatManager.update_chat_history(
                        msg,
                        st.session_state.userInfo,
                        full_response, 
                        target_collection,
                        query_time,
                        st.session_state.curd
                    )

    except Exception as e:
        error_msg = f"聊天初始化失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        st.error(error_msg)
        raise
