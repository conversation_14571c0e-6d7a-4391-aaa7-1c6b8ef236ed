#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: cache_optimizer.py
@time: 2025/7/24
@Description: 缓存优化策略
"""
import time
import pickle
import hashlib
from functools import wraps, lru_cache
from typing import Any, Optional, Callable, Dict, Union
from concurrent.futures import ThreadPoolExecutor

import streamlit as st
import redis
from common.myLog import logger
from config.config_center import settings


class MultiLevelCache:
    """多级缓存系统"""
    
    def __init__(self, redis_url: Optional[str] = None):
        # L1: 内存缓存 (最快)
        self.memory_cache = {}
        self.memory_cache_stats = {"hits": 0, "misses": 0}
        
        # L2: Streamlit session缓存 (中等)
        # 使用 st.session_state
        
        # L3: Redis缓存 (较慢但持久)
        self.redis_client = None
        if redis_url:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=False)
                self.redis_client.ping()
                logger.info("Redis缓存连接成功")
            except Exception as e:
                logger.warning(f"Redis缓存连接失败: {str(e)}")
        
        self.redis_cache_stats = {"hits": 0, "misses": 0}
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{prefix}:{str(args)}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str, level: str = "auto") -> Optional[Any]:
        """获取缓存值"""
        # L1: 内存缓存
        if level in ["auto", "memory"] and key in self.memory_cache:
            self.memory_cache_stats["hits"] += 1
            return self.memory_cache[key]["value"]
        
        # L2: Session缓存
        if level in ["auto", "session"]:
            session_key = f"cache_{key}"
            if session_key in st.session_state:
                value = st.session_state[session_key]
                # 回填到内存缓存
                self.memory_cache[key] = {"value": value, "timestamp": time.time()}
                return value
        
        # L3: Redis缓存
        if level in ["auto", "redis"] and self.redis_client:
            try:
                cached_data = self.redis_client.get(key)
                if cached_data:
                    value = pickle.loads(cached_data)
                    # 回填到上级缓存
                    self.memory_cache[key] = {"value": value, "timestamp": time.time()}
                    st.session_state[f"cache_{key}"] = value
                    self.redis_cache_stats["hits"] += 1
                    return value
            except Exception as e:
                logger.warning(f"Redis缓存读取失败: {str(e)}")
        
        # 缓存未命中
        if level == "memory":
            self.memory_cache_stats["misses"] += 1
        elif level == "redis":
            self.redis_cache_stats["misses"] += 1
        
        return None
    
    def set(self, key: str, value: Any, ttl: int = 3600, level: str = "all"):
        """设置缓存值"""
        timestamp = time.time()
        
        # L1: 内存缓存
        if level in ["all", "memory"]:
            self.memory_cache[key] = {
                "value": value, 
                "timestamp": timestamp,
                "ttl": ttl
            }
        
        # L2: Session缓存
        if level in ["all", "session"]:
            st.session_state[f"cache_{key}"] = value
            st.session_state[f"cache_{key}_timestamp"] = timestamp
            st.session_state[f"cache_{key}_ttl"] = ttl
        
        # L3: Redis缓存
        if level in ["all", "redis"] and self.redis_client:
            try:
                cached_data = pickle.dumps(value)
                self.redis_client.setex(key, ttl, cached_data)
            except Exception as e:
                logger.warning(f"Redis缓存写入失败: {str(e)}")
    
    def delete(self, key: str):
        """删除缓存"""
        # 从所有级别删除
        self.memory_cache.pop(key, None)
        
        session_key = f"cache_{key}"
        if session_key in st.session_state:
            del st.session_state[session_key]
        
        if self.redis_client:
            try:
                self.redis_client.delete(key)
            except Exception as e:
                logger.warning(f"Redis缓存删除失败: {str(e)}")
    
    def clear_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for key, data in self.memory_cache.items():
            if "ttl" in data and current_time - data["timestamp"] > data["ttl"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.memory_cache[key]
        
        logger.info(f"清理过期内存缓存: {len(expired_keys)} 个")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "memory_cache": {
                "size": len(self.memory_cache),
                **self.memory_cache_stats
            },
            "redis_cache": self.redis_cache_stats,
            "redis_connected": self.redis_client is not None
        }


# 全局缓存实例
@lru_cache(maxsize=1)
def get_cache_manager() -> MultiLevelCache:
    """获取缓存管理器单例"""
    redis_url = getattr(settings.database, 'REDIS_URL', None)
    return MultiLevelCache(redis_url)


def smart_cache(
    ttl: int = 3600,
    level: str = "all",
    key_func: Optional[Callable] = None
):
    """智能缓存装饰器"""
    def decorator(func):
        cache_manager = get_cache_manager()
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_key(func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            cached_value = cache_manager.get(cache_key, level)
            if cached_value is not None:
                return cached_value
            
            # 执行函数并缓存结果
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 根据执行时间调整缓存策略
            if execution_time > 1.0:  # 耗时操作使用更长TTL
                cache_ttl = ttl * 2
            else:
                cache_ttl = ttl
            
            cache_manager.set(cache_key, result, cache_ttl, level)
            logger.debug(f"函数 {func.__name__} 执行耗时: {execution_time:.2f}s，已缓存")
            
            return result
        
        # 添加缓存管理方法
        wrapper.cache_clear = lambda: cache_manager.delete(
            cache_manager._generate_key(func.__name__)
        )
        wrapper.cache_stats = cache_manager.get_stats
        
        return wrapper
    return decorator


class PreloadCache:
    """预加载缓存"""
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.preload_tasks = {}
    
    def register_preload(self, key: str, func: Callable, *args, **kwargs):
        """注册预加载任务"""
        self.preload_tasks[key] = (func, args, kwargs)
    
    def start_preload(self):
        """开始预加载"""
        logger.info(f"开始预加载缓存，任务数: {len(self.preload_tasks)}")
        
        for key, (func, args, kwargs) in self.preload_tasks.items():
            future = self.executor.submit(self._preload_task, key, func, *args, **kwargs)
            logger.debug(f"提交预加载任务: {key}")
    
    def _preload_task(self, key: str, func: Callable, *args, **kwargs):
        """执行预加载任务"""
        try:
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 缓存结果
            cache_key = self.cache_manager._generate_key(key, *args, **kwargs)
            self.cache_manager.set(cache_key, result, ttl=7200)  # 2小时TTL
            
            logger.info(f"预加载任务 {key} 完成，耗时: {execution_time:.2f}s")
            
        except Exception as e:
            logger.error(f"预加载任务 {key} 失败: {str(e)}")


# 全局预加载缓存实例
preload_cache = PreloadCache()


def warmup_cache():
    """缓存预热"""
    from common.initialize import init_llm, init_data
    
    # 注册预加载任务
    preload_cache.register_preload("session_data", init_data)
    
    # 开始预加载
    preload_cache.start_preload()
    
    logger.info("缓存预热已启动")


# 优化的缓存装饰器
def optimized_cache(ttl: int = 3600):
    """优化的缓存装饰器，自动选择最佳缓存策略"""
    def decorator(func):
        # 根据函数特性选择缓存级别
        if func.__name__.startswith("get_config"):
            level = "all"  # 配置信息缓存到所有级别
        elif func.__name__.startswith("init_"):
            level = "session"  # 初始化结果缓存到session
        else:
            level = "memory"  # 其他缓存到内存
        
        return smart_cache(ttl=ttl, level=level)(func)
    
    return decorator
