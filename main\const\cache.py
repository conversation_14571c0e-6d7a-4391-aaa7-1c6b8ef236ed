#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存管理模块

提供了一个线程安全的缓存实现，支持：
- 基于LRU的缓存淘汰
- 缓存项过期机制
- 默认值支持
- 批量操作接口
"""
from typing import TypeVar, Generic, Optional, Dict, Any, List, Tuple
from threading import Lock
from time import time
from collections import OrderedDict
from dataclasses import dataclass

T = TypeVar('T')

@dataclass
class CacheItem(Generic[T]):
    """缓存项"""
    value: T
    expire_at: Optional[float] = None  # None表示永不过期

class Cache(Generic[T]):
    """
    通用缓存实现
    
    支持:
    - 基于LRU的缓存淘汰
    - 可配置的过期时间
    - 线程安全操作
    - 批量获取和更新
    """
    
    DEFAULT_CONFIG = {}

    def __init__(self, max_size: int = 500):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数，默认500
        """
        self._max_size = max_size
        self._cache: OrderedDict[str, CacheItem[T]] = OrderedDict()
        self._lock = Lock()
        
        # 初始化默认配置
        for key, value in self.DEFAULT_CONFIG.items():
            self.put(key, value)

    def get(self, key: str, default: Optional[T] = None) -> Optional[T]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 键不存在时返回的默认值
            
        Returns:
            缓存值或默认值
        """
        with self._lock:
            if key not in self._cache:
                return default
                
            item = self._cache[key]
            
            # 检查是否过期
            if item.expire_at and time() > item.expire_at:
                del self._cache[key]
                return default
                
            # 更新访问顺序
            self._cache.move_to_end(key)
            return item.value

    def put(self, key: str, value: T, ttl: Optional[float] = None) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)，None表示永不过期
        """
        with self._lock:
            expire_at = time() + ttl if ttl else None
            
            if key in self._cache:
                del self._cache[key]
            elif len(self._cache) >= self._max_size:
                self._cache.popitem(last=False)  # 删除最早的项
                
            self._cache[key] = CacheItem(value, expire_at)

    def put_many(self, items: Dict[str, T], ttl: Optional[float] = None) -> None:
        """批量设置缓存项"""
        for key, value in items.items():
            self.put(key, value, ttl)

    def get_many(self, keys: List[str], default: Optional[T] = None) -> Dict[str, Optional[T]]:
        """批量获取缓存项"""
        return {key: self.get(key, default) for key in keys}

    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Returns:
            是否删除成功
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False

    def clear(self) -> None:
        """清空所有缓存项"""
        with self._lock:
            self._cache.clear()

    def get_all(self) -> Dict[str, T]:
        """获取所有未过期的缓存项"""
        with self._lock:
            result = {}
            expired_keys = []
            
            for key, item in self._cache.items():
                if item.expire_at and time() > item.expire_at:
                    expired_keys.append(key)
                else:
                    result[key] = item.value
                    
            # 清理过期项
            for key in expired_keys:
                del self._cache[key]
                
            return result

    @property
    def size(self) -> int:
        """当前缓存项数量"""
        return len(self._cache)

    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return self.get(key) is not None

# 全局缓存实例
cache: Cache[Any] = Cache()