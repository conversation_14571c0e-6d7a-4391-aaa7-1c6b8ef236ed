#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
""" 
@author: <PERSON><PERSON><PERSON><PERSON> 
@file: debug.py 
@time: 2025/3/4 11:44
@Description: 
"""
import os

from dotenv import load_dotenv
from langchain_core.prompts import ChatPromptTemplate

from langchain_openai import ChatOpenAI

load_dotenv()

llm =  ChatOpenAI(
        openai_api_base=os.environ.get("BASE_URL"),
        openai_api_key=os.environ.get("API_KEY"),
        model_name=os.environ.get("MODEL"),
        temperature=1
    )

prompt = ChatPromptTemplate.from_template(
    """
    分析以下内容：{rq}
    """
)


if __name__ == '__main__':

    # q = """
    # 按城市为单位展示市场管理端配置的优选门诊
    # """
    #
    # chain = prompt | llm
    # # print(chain.invoke({"rq": q}).content)
    # for i in chain.stream({"rq": q}):
    #     print(i.content, end="", flush=True)
    for i in llm.stream("你是谁"):
        print(i.content, end="", flush=True)