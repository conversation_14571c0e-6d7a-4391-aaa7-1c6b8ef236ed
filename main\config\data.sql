CREATE DATABASE IF NOT EXISTS ym_rag DEFAULT CHARSET utf8 COLLATE utf8_general_ci;

CREATE TABLE `tb_user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `dt_userid` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钉钉userid',
  `dt_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钉钉name',
  `dt_title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钉钉title',
  `dt_mobile` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钉钉mobile',
  `dt_unionid` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '钉钉unionid',
  `roles_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色id，多个以逗号分割',
  `is_super` tinyint(1) NOT NULL COMMENT '超级管理员，1-是 0-否',
  `is_admin` tinyint(1) NOT NULL COMMENT '管理员，1-是 0-否',
  `is_active` tinyint(1) NOT NULL COMMENT '在职状态，1-是 0-否',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_dt_userid` (`dt_userid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';


CREATE TABLE `tb_message` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `dt_userid` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '钉钉userid',
  `dt_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '钉钉name',
  `role` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '角色类型 USER(用户)、SYSTEM(系统)、ASSISTANT(助手)',
  `type` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '问答类型 TQ(技术)、PQ(产品)、CSQ(客服)、AQ(规章制度)',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_role` (`role`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';


CREATE TABLE `tb_roles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `role_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `auth_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限id，多个以逗号分割',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `yn` tinyint(1) NOT NULL COMMENT '0: 未删除 1: 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_role_name` (`role_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';


CREATE TABLE `tb_auths` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `auth_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `yn` tinyint(1) NOT NULL COMMENT '0: 未删除 1: 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_auth_name` (`auth_name`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';


CREATE TABLE `tb_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `config_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `config_value` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置内容',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `yn` tinyint(1) NOT NULL COMMENT '0: 未删除 1: 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_config_name` (`config_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置表';


CREATE TABLE `tb_knowledge` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `document_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名称',
  `section` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属部门',
  `group` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属组',
  `relevance_ai` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联AI',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
  `yn` tinyint(1) NOT NULL COMMENT '0: 未删除 1: 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_section` (`section`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_document_name` (`document_name`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库表';