#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: config_management.py
@time: 2025/7/23 
@Description: 配置管理页面
"""
import streamlit as st
import json
import yaml
from typing import NoReturn

from config.config_center_v2 import get_config_center
from config.config_manager import config_manager
from common.myLog import logger


def render_config_overview():
    """渲染配置概览"""
    st.header("⚙️ 配置管理中心")
    
    config_center = get_config_center()
    summary = config_center.get_config_summary()
    
    # 基本信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("环境", summary["environment"])
    
    with col2:
        st.metric("版本", summary["version"])
    
    with col3:
        debug_status = "开启" if summary["debug"] else "关闭"
        st.metric("调试模式", debug_status)
    
    with col4:
        env_status = "✅ 正常" if config_center.is_production else "⚠️ 开发"
        st.metric("运行状态", env_status)


def render_environment_validation():
    """渲染环境验证"""
    st.subheader("🔍 环境验证")
    
    config_center = get_config_center()
    validation_result = config_center.validate_environment()
    
    # 状态显示
    status_colors = {
        "healthy": "🟢",
        "warning": "🟡",
        "error": "🔴"
    }
    
    status_icon = status_colors.get(validation_result["status"], "⚪")
    st.write(f"{status_icon} **状态**: {validation_result['status']}")
    
    # 显示问题
    if validation_result["issues"]:
        st.error("❌ **发现问题**:")
        for issue in validation_result["issues"]:
            st.write(f"- {issue}")
    
    # 显示警告
    if validation_result["warnings"]:
        st.warning("⚠️ **警告**:")
        for warning in validation_result["warnings"]:
            st.write(f"- {warning}")
    
    if not validation_result["issues"] and not validation_result["warnings"]:
        st.success("✅ 环境配置正常")


def render_database_config():
    """渲染数据库配置"""
    st.subheader("🗄️ 数据库配置")
    
    config_center = get_config_center()
    
    # MySQL配置
    with st.expander("MySQL配置", expanded=False):
        mysql_config = config_center.app.mysql
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**主机**: {mysql_config.host}")
            st.write(f"**端口**: {mysql_config.port}")
            st.write(f"**数据库**: {mysql_config.database}")
        
        with col2:
            st.write(f"**用户名**: {mysql_config.username}")
            st.write(f"**连接URL**: `{mysql_config.url[:50]}...`")
    
    # Redis配置
    with st.expander("Redis配置", expanded=False):
        redis_config = config_center.app.redis
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**主机**: {redis_config.host}")
            st.write(f"**端口**: {redis_config.port}")
        
        with col2:
            st.write(f"**数据库**: {redis_config.database}")
            st.write(f"**连接URL**: `{redis_config.url[:50]}...`")
    
    # Milvus配置
    with st.expander("Milvus配置", expanded=False):
        milvus_config = config_center.app.milvus
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**主机**: {milvus_config.host}")
            st.write(f"**端口**: {milvus_config.port}")
        
        with col2:
            st.write(f"**数据库**: {milvus_config.database}")
            st.write(f"**URI**: {milvus_config.uri}")


def render_ai_config():
    """渲染AI配置"""
    st.subheader("🤖 AI模型配置")
    
    config_center = get_config_center()
    
    # LLM配置
    with st.expander("LLM配置", expanded=False):
        llm_config = config_center.llm_config
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**模型**: {llm_config['model']}")
            st.write(f"**温度**: {llm_config['temperature']}")
            st.write(f"**最大Token**: {llm_config['max_tokens']}")
        
        with col2:
            st.write(f"**基础URL**: `{llm_config['base_url'][:30]}...`")
            st.write(f"**超时时间**: {llm_config['timeout']}秒")
    
    # 嵌入模型配置
    with st.expander("嵌入模型配置", expanded=False):
        embedding_config = config_center.embedding_config
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**模型名称**: {embedding_config['model_name']}")
            st.write(f"**批处理大小**: {embedding_config['batch_size']}")
        
        with col2:
            st.write(f"**最大长度**: {embedding_config['max_length']}")
            if embedding_config['model_path']:
                st.write(f"**本地路径**: `{embedding_config['model_path'][:30]}...`")


def render_cache_config():
    """渲染缓存配置"""
    st.subheader("💾 缓存配置")
    
    config_center = get_config_center()
    cache_config = config_center.cache_config
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("默认TTL", f"{cache_config['default_ttl']}秒")
    
    with col2:
        st.metric("最大大小", cache_config['max_size'])
    
    with col3:
        st.metric("清理间隔", f"{cache_config['cleanup_interval']}秒")
    
    # TTL设置详情
    with st.expander("TTL设置详情", expanded=False):
        ttl_settings = cache_config.get('ttl_settings', {})
        for key, value in ttl_settings.items():
            st.write(f"**{key}**: {value}秒")


def render_business_config():
    """渲染业务配置"""
    st.subheader("💼 业务配置")
    
    config_center = get_config_center()
    
    # 路由规则
    with st.expander("路由规则", expanded=False):
        routing_rules = config_center.routing_rules
        for category, keywords in routing_rules.items():
            st.write(f"**{category}**: {', '.join(keywords)}")
    
    # 默认问题
    with st.expander("默认问题", expanded=False):
        default_questions = config_center.default_questions
        for i, question in enumerate(default_questions, 1):
            st.write(f"{i}. {question}")
    
    # 文件配置
    with st.expander("文件处理配置", expanded=False):
        file_config = config_center.file_config
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**最大文件大小**: {file_config.get('max_file_size', 0) // 1024 // 1024}MB")
            st.write(f"**块大小**: {file_config.get('chunk_size', 500)}")
        
        with col2:
            st.write(f"**块重叠**: {file_config.get('chunk_overlap', 200)}")
            allowed_ext = file_config.get('allowed_extensions', [])
            st.write(f"**支持格式**: {', '.join(allowed_ext)}")


def render_config_operations():
    """渲染配置操作"""
    st.subheader("🛠️ 配置操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 重新加载配置", type="secondary"):
            try:
                config_center = get_config_center()
                config_center.reload_config()
                st.success("✅ 配置重新加载成功")
                st.rerun()
            except Exception as e:
                st.error(f"❌ 配置重新加载失败: {str(e)}")
    
    with col2:
        if st.button("📋 导出配置", type="secondary"):
            try:
                config_center = get_config_center()
                config_summary = config_center.get_config_summary()
                
                config_json = json.dumps(config_summary, indent=2, ensure_ascii=False)
                st.download_button(
                    label="下载配置摘要",
                    data=config_json,
                    file_name="config_summary.json",
                    mime="application/json"
                )
            except Exception as e:
                st.error(f"❌ 配置导出失败: {str(e)}")
    
    with col3:
        if st.button("🔍 配置信息", type="secondary"):
            config_info = config_manager.get_config_info()
            st.json(config_info)


def render_config_editor():
    """渲染配置编辑器"""
    st.subheader("✏️ 配置编辑器")
    
    st.warning("⚠️ 注意: 配置修改需要重启应用才能生效")
    
    config_center = get_config_center()
    yaml_config = config_center.yaml_config
    
    # 配置编辑
    config_text = st.text_area(
        "YAML配置",
        value=yaml.dump(yaml_config, default_flow_style=False, allow_unicode=True),
        height=400,
        help="编辑YAML配置文件内容"
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("验证配置", type="secondary"):
            try:
                yaml.safe_load(config_text)
                st.success("✅ 配置格式正确")
            except yaml.YAMLError as e:
                st.error(f"❌ YAML格式错误: {str(e)}")
    
    with col2:
        if st.button("保存配置", type="primary"):
            try:
                # 验证YAML格式
                new_config = yaml.safe_load(config_text)
                
                # 这里可以添加保存逻辑
                st.success("✅ 配置保存成功 (演示模式)")
                st.info("ℹ️ 实际环境中需要重启应用")
                
            except Exception as e:
                st.error(f"❌ 配置保存失败: {str(e)}")


async def init_config_management() -> NoReturn:
    """配置管理页面初始化"""
    try:
        st.title("⚙️ 配置管理系统")
        
        # 渲染各个部分
        render_config_overview()
        st.write("---")
        render_environment_validation()
        st.write("---")
        render_database_config()
        st.write("---")
        render_ai_config()
        st.write("---")
        render_cache_config()
        st.write("---")
        render_business_config()
        st.write("---")
        render_config_operations()
        st.write("---")
        render_config_editor()
        
    except Exception as e:
        logger.error(f"配置管理页面错误: {str(e)}", exc_info=True)
        st.error(f"页面加载失败: {str(e)}")
